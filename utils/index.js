const { executeQuery } = require('../config/dbConfig');

exports.generateOtp = () => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

exports.combineSkills = (data) => {
  return data.map((user) => {
    // Ensure skills and other_skills are arrays
    const skillsArray = Array.isArray(user.skills) ? user.skills : [];
    const otherSkillsArray = Array.isArray(user.other_skills)
      ? user.other_skills
      : [];

    // Combine skills and other skills into a single array
    delete user.other_skills;
    const combinedSkills = [...skillsArray, ...otherSkillsArray].filter(
      (skill) => skill && skill.id !== null && skill.name !== null,
    );

    // Remove duplicate skills based on the 'id' field
    const uniqueSkills = Array.from(
      new Map(combinedSkills.map((skill) => [skill.id, skill])).values(),
    );

    // Update the user object with the combined and unique skills
    return {
      ...user,
      skills: uniqueSkills,
    };
  });
};

exports.insertAction = async (req, type) => {
  const userId = req.userData.id;
  const { devicename, latitude, longitude, ip } = req.headers;

  const activityInsertStatus = await executeQuery(
    'CALL AddUserActivity(?,?,?,?,?,?)',
    [userId, type, ip, latitude, longitude, devicename],
  );

  if (activityInsertStatus[0].length) {
    const { activity_id } = activityInsertStatus[0][0];
    return activity_id;
  } else {
    return 0;
  }
};

exports.groupByKey = (data, groupKey) => {
  return data.reduce((acc, item) => {
    // Find the group by the provided groupKey (e.g., master_skill)
    let group = acc.find((group) => group.key === item[groupKey]);

    // If the group doesn't exist, create a new one
    if (!group) {
      group = {
        id: item['master_id'],
        key: item[groupKey],
        values: [],
      };
      acc.push(group);
    }

    // Add all the properties except the groupKey to the values array
    // eslint-disable-next-line no-unused-vars
    const {
      // eslint-disable-next-line no-unused-vars
      [groupKey]: _,
      // eslint-disable-next-line no-unused-vars
      master_id,
      ...values
    } = item;
    group.values.push(values);

    return acc;
  }, []);
};

function replaceTextValue(message, replacements) {
  console.log(message, replacements, 'replacements');
  const replacedString = message.replace(/\${(\d)}/g, (match, index) => {
    return replacements[index];
  });
  return replacedString;
}

function formatData(results) {
  const formattedData = [];

  results.forEach((row) => {
    // Check if the country already exists in the formattedData
    let countryEntry = formattedData.find((item) => item.value == row.country);

    if (!countryEntry) {
      // If country doesn't exist, create a new country entry
      countryEntry = {
        key: 'Country',
        value: row.country,
        count: 0, // Track total entries in the country
        stateCount: 0, // Track unique states in the country
        cityCount: 0, // Track unique cities in the country
        data: [],
      };
      formattedData.push(countryEntry);
    }

    // Check if the state already exists in the country's data
    let stateEntry = countryEntry.data.find((item) => item.value == row.state);

    if (!stateEntry) {
      // If state doesn't exist, create a new state entry
      stateEntry = {
        key: 'State',
        value: row.state,
        count: 0, // Track total entries in the state
        cityCount: 0, // Track unique cities in the state
        data: [],
      };
      countryEntry.data.push(stateEntry);
      countryEntry.stateCount++; // Increment unique state count
    }

    // Check if the city already exists in the state's data
    let cityEntry = stateEntry.data.find((item) => item.value == row.city);

    if (!cityEntry) {
      // If city doesn't exist, create a new city entry
      cityEntry = {
        key: 'City',
        value: row.city,
        count: 1, // Initialize city count as 1
      };
      stateEntry.data.push(cityEntry);
      stateEntry.cityCount++; // Increment unique city count in the state
      countryEntry.cityCount++; // Increment unique city count in the country
    } else {
      // If city exists, increment the count
      cityEntry.count++;
    }

    // Increment the state and country total counts for each row
    stateEntry.count++;
    countryEntry.count++;
  });

  return formattedData;
}
const calculatePercentageChange = (current, previous) => {
  console.log(current, previous, 'current, previous');
  // Convert string inputs to numbers if possible
  const currentValue = typeof current === 'string' ? Number(current) : current;
  const previousValue =
    typeof previous === 'string' ? Number(previous) : previous;

  // Ensure both inputs are valid numbers and not NaN
  if (isNaN(currentValue) || isNaN(previousValue)) {
    return '0.00'; // Default value in case of invalid input
  }

  // Handle zero previous count to avoid division by zero
  if (previousValue === 0) {
    return currentValue > 0 ? currentValue.toFixed(2).toString() : '00.00'; // Return full percentage increase or decrease
  }

  // Calculate the percentage change
  const percentageChange =
    ((currentValue - previousValue) / previousValue) * 100;
  return percentageChange.toFixed(2);
};

// function calculateViewCountChange(currentWeekStats, previousWeekStats) {
//   const result = [];

//   currentWeekStats.forEach((currentCountry) => {
//     const previousCountry = previousWeekStats.find(
//       (p) => p.value === currentCountry.value,
//     );

//     if (previousCountry) {
//       const currentCountryCount = isNaN(currentCountry.count)
//         ? 0
//         : currentCountry.count;

//       const countryResult = {
//         key: 'Country',
//         value: currentCountry.value || 'Unknown',
//         count: currentCountryCount,
//         data: [],
//       };

//       // Now iterate through the states
//       if (Array.isArray(currentCountry.data)) {
//         currentCountry.data.forEach((currentState) => {
//           const previousState = previousCountry.data.find(
//             (p) => p.value === currentState.value,
//           );

//           if (previousState) {
//             const currentStateCount = isNaN(currentState.count)
//               ? 0
//               : currentState.count;

//             const stateResult = {
//               key: 'State',
//               value: currentState.value || 'Unknown',
//               count: currentStateCount,
//               data: [],
//             };

//             // Now iterate through the cities
//             if (Array.isArray(currentState.data)) {
//               currentState.data.forEach((currentCity) => {
//                 const previousCity = previousState.data.find(
//                   (p) => p.value === currentCity.value,
//                 );

//                 if (previousCity) {
//                   const currentCityCount = isNaN(currentCity.count)
//                     ? 0
//                     : currentCity.count;

//                   const cityResult = {
//                     key: 'City',
//                     value: currentCity.value || 'N/A',
//                     count: currentCityCount,
//                   };

//                   stateResult.data.push(cityResult);
//                 }
//               });
//             }

//             countryResult.data.push(stateResult);
//           }
//         });
//       }

//       result.push(countryResult);
//     }
//   });

//   return result;
// }
function calculateViewCountChange(currentWeekStats, previousWeekStats) {
  const result = [];

  currentWeekStats.forEach((currentCountry) => {
    const previousCountry = previousWeekStats.find(
      (p) => p.value === currentCountry.value,
    );

    const currentCountryCount = isNaN(currentCountry.count)
      ? 0
      : currentCountry.count;

    const countryResult = {
      key: 'Country',
      value: currentCountry.value || 'Unknown',
      count: currentCountryCount,
      data: [],
    };

    if (Array.isArray(currentCountry.data)) {
      currentCountry.data.forEach((currentState) => {
        const previousState = previousCountry?.data.find(
          (p) => p.value === currentState.value,
        );

        const currentStateCount = isNaN(currentState.count)
          ? 0
          : currentState.count;

        const stateResult = {
          key: 'State',
          value: currentState.value || 'Unknown',
          count: currentStateCount,
          data: [],
        };

        if (Array.isArray(currentState.data)) {
          currentState.data.forEach((currentCity) => {
            const previousCity = previousState?.data.find(
              (p) => p.value === currentCity.value,
            );

            const currentCityCount = isNaN(currentCity.count)
              ? 0
              : currentCity.count;

            const previousCityCount = previousCity ? previousCity.count : 0;

            const cityResult = {
              key: 'City',
              value: currentCity.value || 'N/A',
              count: currentCityCount,
              change: currentCityCount - previousCityCount, // Calculate change in view count
            };

            stateResult.data.push(cityResult);
          });
        }

        countryResult.data.push(stateResult);
      });
    }

    result.push(countryResult);
  });

  return result;
}

exports.globalVar = {
  replaceTextValue,
  formatData,
  calculateViewCountChange,
  calculatePercentageChange,
};

exports.getTemplates = (type) => {
  return executeQuery('SELECT * FROM templates where module=?', [type]);
};
exports.getUserData = async (id) => {
  const userName = await executeQuery('Call GetUserData(?)', [id]);
  return userName[0];
};
exports.getUserFcm = async (id) => {
  const userName = await executeQuery('Call GetUserFcm(?)', [id]);
  return userName[0][0]?.fcm_token || '';
};
exports.getUserMatchId = async (id, other_user_id) => {
  const userName = await executeQuery('Call GetUserMatchid(?,?)', [
    id,
    other_user_id,
  ]);
  return userName[0][0]?.id || '';
};
exports.getSubscriptionPlan = async (id) => {
  const planName = await executeQuery(
    'SELECT plan_name  FROM subscription_plans where id=?',
    [id],
  );

  console.log(planName[0]?.plan_name, 'llll');
  return planName[0]?.plan_name || '';
};
exports.getStatus = async (id) => {
  const planName = await executeQuery(
    'SELECT status_name from  status where id=?',
    [id],
  );
  return planName[0]?.status_name || '';
};

exports.formatFeatures = (features) => {
  return features.map((feature) => {
    return {
      feature_id: feature.feature_id,
      feature_name: feature.feature_name,
      feature_description: feature.feature_description,
      plans: feature.plans.map((plan) => ({
        key: plan.plan_name,
        planId: plan.plan_id,
        keyFeatures: {
          included: true,
          time_limit_minutes: plan.time_limit_minutes ?? 0, // Convert null to 0
        },
      })),
    };
  });
};

exports.groupUsersByLocation = (data) => {
  if (!Array.isArray(data) || data.length === 0) {
    return [];
  }

  const groupedData = {};

  data.forEach((user) => {
    if (
      !user.latitude ||
      !user.longitude ||
      !user.id ||
      !user.full_name ||
      !user.media_url
    ) {
      return; // Skip invalid or incomplete entries
    }

    const key = `${user.latitude},${user.longitude}`;

    if (!groupedData[key]) {
      groupedData[key] = {
        lat: parseFloat(user.latitude),
        lng: parseFloat(user.longitude),
        users: [],
      };
    }

    groupedData[key].users.push({
      id: user.id,
      name: user.full_name,
      pic: user.media_url,
    });
  });

  return Object.values(groupedData);
};
