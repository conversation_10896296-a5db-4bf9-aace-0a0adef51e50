const FromEmail = process.env.NODE_MAIL_USER;

const fs = require('fs');
const path = require('path');

const welcomeTemplatePath = path.join(
  __dirname,
  'emailTemplates',
  'Welcome.html',
);
const otpTemplatePath = path.join(__dirname, 'emailTemplates', 'Otp.html');
const welcomeTemplate = fs.readFileSync(welcomeTemplatePath, 'utf8');

const otpTemplate = fs.readFileSync(otpTemplatePath, 'utf8');

const registerTemplatePath = path.join(
  __dirname,
  'emailTemplates',
  'RegisterSuccess.html',
);
const registerTemplate = fs.readFileSync(registerTemplatePath, 'utf8');

const subscriptionTemplatePath = path.join(
  __dirname,
  'emailTemplates',
  'Subscription.html',
);
const subscriptionTemplate = fs.readFileSync(subscriptionTemplatePath, 'utf8');

const pitchTemplatePath = path.join(
  __dirname,
  'emailTemplates',
  'PitchStatus.html',
);
const pitchTemplate = fs.readFileSync(pitchTemplatePath, 'utf8');

const pitchSuspend = path.join(
  __dirname,
  'emailTemplates',
  'SuspendAccount.html',
);
const pitchSuspendTemp = fs.readFileSync(pitchSuspend, 'utf8');

const ContactUs = path.join(__dirname, 'emailTemplates', 'Contact.html');
const contactusTemp = fs.readFileSync(ContactUs, 'utf8');
exports.mailRegister = (name, email) => {
  const Html = registerTemplate.replace('${name}', name);

  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: `You’e In! Welcome to TechNBuddy, ${name}`,
    html: Html,
  };
};

exports.otpMail = (email) => {
  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: '[TechNBuddy] - Verify Otp',
    html: '',
  };
};

exports.ResendOtpMail = (email) => {
  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: '[TechNBuddy] - Verify Otp',
    html: '',
  };
};

exports.mailOptionsGreet = (name, email) => {
  const personalizedHtml = welcomeTemplate.replace('${name}', name);
  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: 'Welcome to TechNBuddy',
    html: personalizedHtml,

    // htmlTemplate(WelcomeGreetContent(name)),
  };
};

exports.subscriptionMail = (name, email, planName, startDate, endDate) => {
  console.log(email, 'ggggg');

  let personalizedHtml = subscriptionTemplate
    .replace('${name}', name)
    .replace('${plan_name}', planName)
    .replace('${plan_name}', planName)
    .replace('${start_date}', startDate)
    .replace('${end_date}', endDate);

  return {
    from: FromEmail, // process.env.EMAIL
    to: email,
    subject: 'You’e In! Your TechNBuddy Subscription Is Active',
    html: personalizedHtml,
  };
};

exports.pitchStatusMail = (name, email, statusMessage, status) => {
  let personalizedHtml = pitchTemplate
    .replace('${name}', name)
    .replace('${status}', status)
    .replace('${status}', status)
    .replace('${statusMessage}', statusMessage);

  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: `Pitch Status Update – ${status}`,
    html: personalizedHtml,
  };
};

exports.mailSuspend = (name, email, reason) => {
  // console.log( email, firstname,lastname, password,"kkkkkk")
  let personalizedHtml = pitchSuspendTemp
    .replace('${name}', name)
    .replace('${reason}', reason);
  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: 'Action Required: Your Techbuddy account has been suspended ',
    html: personalizedHtml,
  };
};

exports.mailContactUs = (name, email, jobTitle, phone, message) => {
  // console.log( email, firstname,lastname, password,"kkkkkk")
  let personalizedHtml = contactusTemp
    .replace('${name}', name)
    .replace('${email}', email)
    .replace('${phone}', phone)
    .replace('${jobTitle}', jobTitle)
    .replace('${message}', message);

  return {
    from: email,

    to: FromEmail,
    subject: `New Query from ${name}`,
    html: personalizedHtml,
  };
};

exports.mailOptions = (otp, email) => {
  const personalizedHtml = otpTemplate.replace('${otp}', otp);
  return {
    from: {
      email: FromEmail,
      name: 'techNbuddy',
    },

    to: email,
    subject: 'Welcome to TechNBuddy',
    html: personalizedHtml,

    // htmlTemplate(WelcomeGreetContent(name)),
  };
};
