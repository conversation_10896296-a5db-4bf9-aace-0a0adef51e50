const algoliasearch = require('algoliasearch');
const {
  algoliaAppId,
  algoliaAdminApiKey,
  algoliaIndexName,
} = require('../config');
const { executeQuery } = require('../config/dbConfig');
const { groupByKey } = require('.');

// Initialize Algolia client
const client = algoliasearch(algoliaAppId, algoliaAdminApiKey);
const index = client.initIndex(algoliaIndexName); // Initialize index properly

exports.getAlgoliaUsers = async (
  query = '',
  filters = '',
  page = 0,
  hitsPerPage = 20,
  optionalFilters = [],
  geoSearchParams = {},
) => {
  try {
    // Use the initialized index to search
    const searchResults = await index.search(query, {
      page,
      hitsPerPage,
      filters,
      optionalFilters,
      ...geoSearchParams, // Include geo search parameters
    });

    console.log('Search results:', searchResults);
    return searchResults;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};

exports.uploadUsersToAlgolia = async () => {
  try {
    const usersRaw = await executeQuery('CALL GetAllUsers()');

    // If users are wrapped inside result set due to stored procedure
    const users = usersRaw[0] || usersRaw;
    if (!users || users.length === 0) {
      console.log('No users found to upload to Algolia.');
      return;
    }
    for (const user of users) {
      const profileDetails = await executeQuery('CALL GetProfileDetails(?)', [
        user.id,
      ]);
      const response = await index.saveObject({
        objectID: user.id,
        ...profileDetails[0][0],
        skills: groupByKey(profileDetails[1], 'master_skill'), // Use standardized skills object
        educations: profileDetails[2],
        experience: profileDetails[3],
        interest: profileDetails[4],
        languages: profileDetails[5],
        links: profileDetails[6],
        profileComplete: profileDetails[7][0].profileComplete,
      });
      console.log('Algolia response:', response);
    }
  } catch (error) {
    console.error('Error uploading users to Algolia:', error);
  }
};
