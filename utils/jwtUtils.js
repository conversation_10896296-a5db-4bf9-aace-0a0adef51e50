const jwt = require('jsonwebtoken'); // Replace with your actual data access functions
const config = require('../config');
const { executeQuery } = require('../config/dbConfig');

// Custom JWT middleware
const verifyToken = async (req, res, next) => {
  // Get auth header value
  const bearerHeader = req.headers['authorization'];
  const { latitude, longitude, address } = req.headers;
  if (!bearerHeader) {
    return res
      .status(401)
      .json({ status: false, message: 'No token provided', data: {} });
  }
  // Split the header and get the token
  const bearer = bearerHeader.split(' ');
  const token = bearer[1];
  try {
    const decoded = jwt.verify(token, config.jwtAccessToken);
    const isValid = await executeQuery('CALL IsValidUser(?,?,?,?,?)', [
      decoded.id,
      token,
      latitude,
      longitude,
      address,
    ]);
    if (isValid[0][0].isValidUser) {
      req.userData = decoded;
      req.token = token;
      req.sessionId = isValid[0][0].sessionId;
      next();
    } else {
      return res
        .status(401)
        .json({ status: false, message: isValid[0][0].message, data: {} });
    }
  } catch (err) {
    console.error('Token verification error:', err);
    return res
      .status(401)
      .json({ status: false, message: 'Unauthorize', data: {} });
  }
};
const verifyAdminToken = async (req, res, next) => {
  // Get auth header value
  const bearerHeader = req.headers['authorization'];

  if (!bearerHeader) {
    return res
      .status(401)
      .json({ status: false, message: 'No token provided', data: {} });
  }
  // Split the header and get the token
  const bearer = bearerHeader.split(' ');
  const token = bearer[1];
  try {
    const decoded = jwt.verify(token, config.jwtAccessToken);

    if (decoded) {
      req.userData = decoded;
      req.token = token;
      next();
    } else {
      return res
        .status(401)
        .json({ status: false, message: 'Unauthorize', data: {} });
    }
  } catch (err) {
    console.error('Token verification error:', err);
    return res
      .status(401)
      .json({ status: false, message: 'Unauthorize', data: {} });
  }
};
const generateToken = async (id) => {
  return jwt.sign(
    {
      id: id,
    },
    config.jwtAccessToken,
  );
};

module.exports = {
  verifyToken,
  generateToken,
  verifyAdminToken,
};
