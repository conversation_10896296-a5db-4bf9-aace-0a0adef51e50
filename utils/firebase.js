const { admin } = require('../config/firebaseConfig');

exports.notification = async (
  title,
  body,
  registrationToken,
  data,
  type,
  isTopic = false,
) => {
  try {
    // Validate registration token
    if (!registrationToken || registrationToken.trim() === '') {
      console.warn('Invalid or empty registration token provided');
      return;
    }

    const message = {
      notification: {
        title,
        body,
      },
      data: data, // You can pass additional data here, like click_action, sound, etc
      android: {
        notification: {
          click_action:
            type == 'Call' ? 'OPEN_ACTIVITY_CALL' : 'OPEN_ACTIVITY_MAIN',
          body: body,
          image: data.sender_profile_pic || '',
          sound: 'notificationsound.mp3',
        },
        priority: 'High',
      },
    };

    message[isTopic ? 'topic' : 'token'] = registrationToken;
    console.log(message, isTopic, 'message 123');

    try {
      const response = await admin.messaging().send(message);
      console.log('Notification sent:', response);
      return response;
    } catch (error) {
      console.error('Error sending notification:', error);

      // Handle specific Firebase messaging errors
      if (error.code === 'messaging/registration-token-not-registered') {
        console.warn(
          `Token not registered: ${registrationToken}. Token may be invalid or expired.`,
        );
        // TODO: Remove invalid token from database
        // await removeInvalidToken(registrationToken);
      } else if (error.code === 'messaging/invalid-registration-token') {
        console.warn(`Invalid token format: ${registrationToken}`);
        // TODO: Remove invalid token from database
      } else if (error.code === 'messaging/mismatched-credential') {
        console.error('Firebase credentials mismatch');
      }

      throw error; // Re-throw for upstream handling
    }
  } catch (err) {
    console.error('Unexpected error occurred:', err);
    throw err;
  }
};

exports.notificationForCall = (
  title,
  body,
  registrationToken,
  data,
  channelBody,
) => {
  try {
    const message = {
      token: registrationToken,
      android: {
        notification: {
          icon: 'ic_call_notification',
          title: 'Incoming Call',
          body: 'Call from John Doe',
          priority: 'high',
          sound: 'content://settings/system/ringtone_default',
          channelId: channelBody?.id || 'high_importance_channel', // Fallback channel ID
          clickAction: 'OPEN_ACTIVITY_CALL',
          // ongoing: true,
          // category: 'CALL',
          visibility: 'public',
        },
        data: {
          callType: 'incoming',
          callerName: 'John Doe',
          callerId: '10190',
          target_activity: 'com.technbuddy.app.videoCall.VideoChatViewActivity',
          target_service:
            'com.technbuddy.app.helper.NotificationDismissReceiver',
          action: 'STOP_RINGTONE',
          icon: 'ic_answer',
          ...data, // Include any additional data
        },
      },
    };

    console.log(message, 'message 123');

    admin
      .messaging()
      .send(message)
      .then((response) => {
        console.log('Notification sent:', response);
      })
      .catch((error) => {
        console.error('Error sending notification:', error);
      });
  } catch (err) {
    console.error('Unexpected error occurred:', err);
  }
};

exports.sendBulkNotifications = async (userIds, tokens, payload) => {
  try {
    const response = await admin.messaging().sendToDevice(tokens, payload, {
      priority: 'high',
      timeToLive: 60 * 60 * 24,
    });

    // Object to hold successful and failed tokens along with their corresponding user_ids
    const successTokens = {};
    const failedTokens = {};

    // Check each result to determine success or failure
    response.results.forEach((result, index) => {
      const { error, message_id } = result;
      if (error) {
        // Token failed to receive the notification
        failedTokens[userIds[index]] = tokens[index];
        console.error(
          `Failed to send notification to token: ${tokens[index]} for user id: ${userIds[index]}. Error:`,
          error,
        );
      } else {
        // Token successfully received the notification
        successTokens[userIds[index]] = tokens[index];
        console.log(
          `Notification sent successfully to token: ${tokens[index]} for user id: ${userIds[index]}. Message ID: ${message_id}`,
        );
      }
    });
    // Return an object containing both successful and failed tokens along with their corresponding user_ids
    return { successTokens, failedTokens };
  } catch (error) {
    console.error('Error sending bulk notifications:', error);
    throw error;
  }
};

exports.subscribeUserToTopic = async (token) => {
  try {
    if (!token || token.trim() === '') {
      console.warn('Cannot subscribe: Invalid or empty FCM token');
      return;
    }

    await admin.messaging().subscribeToTopic(token, 'all_users');
    console.log('Successfully subscribed to topic:', token);
  } catch (error) {
    console.error('Error subscribing user to topic:', error);

    // Handle specific errors
    if (error.code === 'messaging/registration-token-not-registered') {
      console.warn(`Cannot subscribe: Token not registered - ${token}`);
      // TODO: Remove invalid token from database
    } else if (error.code === 'messaging/invalid-registration-token') {
      console.warn(`Cannot subscribe: Invalid token format - ${token}`);
      // TODO: Remove invalid token from database
    }
  }
};
exports.unSubscribeUserToTopic = async (token) => {
  try {
    if (!token || token.trim() === '') {
      console.warn('Cannot unsubscribe: Invalid or empty FCM token');
      return;
    }

    await admin.messaging().unsubscribeFromTopic(token, 'all_users');
    console.log('Successfully Unsubscribed from topic:', token);
  } catch (error) {
    console.error('Error unsubscribing user from topic:', error);

    // Handle specific errors
    if (error.code === 'messaging/registration-token-not-registered') {
      console.warn(`Cannot unsubscribe: Token not registered - ${token}`);
    } else if (error.code === 'messaging/invalid-registration-token') {
      console.warn(`Cannot unsubscribe: Invalid token format - ${token}`);
    }
  }
};

// Utility function to validate and clean up FCM tokens
exports.validateAndCleanupTokens = async (tokens) => {
  const { executeQuery } = require('../config/dbConfig');

  if (!Array.isArray(tokens)) {
    tokens = [tokens];
  }

  const validTokens = [];
  const invalidTokens = [];

  for (const token of tokens) {
    if (!token || token.trim() === '') {
      invalidTokens.push(token);
      continue;
    }

    try {
      // Test the token by sending a dry-run message
      await admin.messaging().send(
        {
          token: token,
          data: { test: 'true' },
        },
        true,
      ); // dry-run mode

      validTokens.push(token);
    } catch (error) {
      console.warn(`Invalid FCM token detected: ${token}`, error.code);
      invalidTokens.push(token);

      // Remove invalid token from database
      try {
        await executeQuery('CALL RemoveInvalidFCMToken(?)', [token]);
        console.log(`Removed invalid FCM token from database: ${token}`);
      } catch (dbError) {
        console.error('Error removing invalid token from database:', dbError);
      }
    }
  }

  return { validTokens, invalidTokens };
};

// await notificationForCall(
//   userData[0].name,
//   'call',
//   fcmData,
//   {
//     match_id: getMatchData.toString(),
//     sender_id: uid.toString(),
//     sender_name: userData[0].name,
//     // message: message,
//     type: 'Call',
//     sender_profile_pic: userData[0].profile_pic,
//     created_at: new Date(now_utc).toISOString(),
//   },
//   {
//     id: "techNbuddy_channel102",
//     name: 'techNBuddy Channel',
//     description: "Channel for incoming call notifications",
//     importance: "HIGH",
//     sound: {
//       uri: "content://settings/system/ringtone_default",
//       audio_attributes: {
//         usage: "NOTIFICATION_RINGTONE",
//         content_type: "SONIFICATION"
//       }

//     },

//     vibration: true,

//     vibration_pattern: [1000, 1000],

//     lockscreen_visibility: "PUBLIC"

//   }
//   ,

// );
