const Joi = require('joi');

exports.reportSchema = Joi.object({
  report_user_id: Joi.number().required().min(5),
  report_reason_id: Joi.number().required(),
  description: Joi.string().allow(''),
  attachment_link: Joi.string().allow(''),
});

exports.blockSchema = Joi.object({
  block_user_id: Joi.number().required(),
});
exports.unblockSchema = Joi.object({
  unblock_user_id: Joi.number().required(),
});
exports.likelistSchema = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
});

exports.matchListSchema = Joi.object({
  page_size: Joi.number().integer().min(1).max(100).default(10),
  page_number: Joi.number().integer().min(1).default(1),
});

exports.viewPitchSchema = Joi.object({
  pitch_id: Joi.number().required(),
  duration: Joi.number().required(),
  city: Joi.string().allow(''),
  state: Joi.string().required(),
  country: Joi.string().required(),
  area: Joi.string(),
});

exports.viewPitchDetailsSchema = Joi.object({
  pitch_id: Joi.number().required(),
  start_date: Joi.date().iso().required(),
  end_date: Joi.date()
    .iso()
    .greater(Joi.ref('start_date'))
    .required()
    .messages({
      'date.greater': 'End date must be greater than start date',
      'date.format': 'Dates must be in YYYY-MM-DD format',
    }),
});
exports.searchSchema = Joi.object({
  search: Joi.string().required(),
});

exports.removeMatch = Joi.object({
  match_id: Joi.number().required().min(1),
  reason: Joi.string().min(1),
});
exports.removeLike = Joi.object({
  liked_id: Joi.number().required().min(1),
});

exports.updateVideoCallHistory = Joi.object({
  call_id: Joi.number().required(),
  start_time: Joi.date().iso().required(),
  end_time: Joi.date().iso().required(),
  status: Joi.string()
    .required()
    .valid('ongoing', 'completed', 'missed', 'cancelled'),
});
