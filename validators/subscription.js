const Joi = require('joi');
const config = require('../config');
const jwt = require('jsonwebtoken');

exports.addPlan = Joi.object({
  plan_id: Joi.number().valid(1, 2).required(),
  device_type: Joi.string().valid('Android', 'Ios').required(),
  purchase_token: Joi.string().required(),
  transaction_id: Joi.string().required(),
  amount: Joi.number().precision(2).required(),
  currency: Joi.string()
    .uppercase()
    .length(3)
    .regex(/^[A-Z]{3}$/) // Ensures it's a valid 3-letter currency code
    .required(),
});

exports.validatePromoCode = Joi.object({
  promo_code: Joi.string().required(),
});

// Generic webhook schema that can handle both platforms
const webhookSchema = Joi.object({
  platform: Joi.string().valid('android', 'ios').required(),
  event: Joi.string().required(),
  data: Joi.object({
    subscriptionId: Joi.string().required(),
    expiryDate: Joi.string().isoDate().optional(),
    status: Joi.string().optional(),

    // Android specific
    purchaseToken: Joi.string().optional(),
    notificationType: Joi.number().optional(),

    // iOS specific
    transactionId: Joi.string().optional(),
    autoRenewStatus: Joi.alternatives()
      .try(Joi.string(), Joi.number())
      .optional(),
    unifiedReceipt: Joi.object().optional(),

    // Common metadata
    metadata: Joi.object().optional(),
  }).required(),
});

// Apple App Store webhook schema
const appleWebhookSchema = Joi.object({
  notification_type: Joi.string().required(),
  unified_receipt: Joi.object({
    latest_receipt_info: Joi.alternatives().try(
      Joi.array().items(
        Joi.object({
          product_id: Joi.string().required(),
          transaction_id: Joi.string().required(),
          expires_date: Joi.string().required(),
          auto_renew_status: Joi.alternatives()
            .try(Joi.string(), Joi.number())
            .optional(),
        }),
      ),
      Joi.object(),
    ),
  }).required(),
  environment: Joi.string().valid('PROD', 'SANDBOX').required(),
});

function getGoogleEventType(notificationType) {
  const typeMap = {
    1: 'SUBSCRIPTION_RECOVERED',
    2: 'SUBSCRIPTION_RENEWED',
    3: 'SUBSCRIPTION_CANCELED',
    4: 'SUBSCRIPTION_PURCHASED',
    5: 'SUBSCRIPTION_ON_HOLD',
    6: 'SUBSCRIPTION_IN_GRACE_PERIOD',
    7: 'SUBSCRIPTION_RESTARTED',
    8: 'SUBSCRIPTION_PRICE_CHANGE_CONFIRMED',
    9: 'SUBSCRIPTION_DEFERRED',
    10: 'SUBSCRIPTION_PAUSED',
    11: 'SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED',
    12: 'SUBSCRIPTION_REVOKED',
    13: 'SUBSCRIPTION_EXPIRED',
  };
  return typeMap[notificationType] || 'UNKNOWN';
}

function getAppleEventType(notificationType) {
  const typeMap = {
    INITIAL_BUY: 'SUBSCRIPTION_PURCHASED',
    RENEWAL: 'SUBSCRIPTION_RENEWED',
    CANCEL: 'SUBSCRIPTION_CANCELED',
    DID_CHANGE_RENEWAL_STATUS: 'SUBSCRIPTION_STATUS_CHANGED',
    DID_FAIL_TO_RENEW: 'SUBSCRIPTION_FAILED',
    EXPIRED: 'SUBSCRIPTION_EXPIRED',
  };
  return typeMap[notificationType] || notificationType;
}

// Function to verify Apple's signed payload
async function verifyAppleSignedPayload(payload) {
  try {
    if (!payload.signedPayload) {
      throw new Error('Missing signed payload');
    }

    const decoded = jwt.verify(payload.signedPayload, config.appleWebhookKey, {
      algorithms: ['ES256'],
      issuer: config.appleWebhookIssuerId,
      audience: config.appleWebhookAppBundleId,
      keyid: config.appleWebhookKeyId,
    });

    return decoded;
  } catch (error) {
    console.error('Error verifying Apple signed payload:', error);
    throw error;
  }
}

exports.validateWebhookPayload = (body) => {
  console.log('Received webhook payload:', JSON.stringify(body, null, 2));

  // Handle payload wrapped in value property
  const payload = body.value || body;

  // 1. Handle Google webhook format (Pub/Sub)
  if (payload?.message?.data) {
    try {
      const decodedData = Buffer.from(payload.message.data, 'base64').toString(
        'utf-8',
      );
      const parsedData = JSON.parse(decodedData);

      console.log(
        'Decoded Google webhook data:',
        JSON.stringify(parsedData, null, 2),
      );

      const notificationData =
        parsedData.subscriptionNotification ||
        parsedData.oneTimeProductNotification ||
        {};

      const subscriptionId =
        notificationData.subscriptionId ||
        notificationData.purchaseToken ||
        notificationData.productId;

      if (!subscriptionId) {
        return {
          error: new Error('Missing subscription ID in Google webhook data'),
        };
      }

      return {
        value: {
          platform: 'android',
          event: getGoogleEventType(notificationData.notificationType),
          data: {
            subscriptionId,
            purchaseToken: notificationData.purchaseToken,
            notificationType: notificationData.notificationType,
            expiryDate: notificationData.expiryDate,
            metadata: {
              versionCode: notificationData.versionCode,
              rawData: notificationData,
              messageId: payload.message.messageId,
              publishTime: payload.message.publishTime,
            },
          },
        },
      };
    } catch (err) {
      console.error('Error decoding/parsing Google data:', err);
      return {
        error: new Error('Invalid Google webhook data: ' + err.message),
      };
    }
  }

  // 2. Handle Apple webhook format
  if (payload.signedPayload) {
    try {
      const verifiedPayload = verifyAppleSignedPayload(payload);
      const { error: appleError, value: appleValue } =
        appleWebhookSchema.validate(verifiedPayload);

      if (!appleError) {
        console.log(
          'Parsed Apple webhook data:',
          JSON.stringify(appleValue, null, 2),
        );

        const latestReceipt = Array.isArray(
          appleValue.unified_receipt.latest_receipt_info,
        )
          ? appleValue.unified_receipt.latest_receipt_info[0] || {}
          : appleValue.unified_receipt.latest_receipt_info || {};

        return {
          value: {
            platform: 'ios',
            event: getAppleEventType(appleValue.notification_type),
            data: {
              subscriptionId: latestReceipt.product_id,
              transactionId: latestReceipt.transaction_id,
              expiryDate: latestReceipt.expires_date,
              autoRenewStatus: latestReceipt.auto_renew_status,
              metadata: {
                environment: appleValue.environment,
                unifiedReceipt: appleValue.unified_receipt,
              },
            },
          },
        };
      }
    } catch (err) {
      console.error('Error verifying/parsing Apple data:', err);
      return {
        error: new Error('Invalid Apple webhook data: ' + err.message),
      };
    }
  }

  // 3. Try generic webhook format
  const { error: genericError, value: genericValue } =
    webhookSchema.validate(payload);
  if (!genericError) {
    return { value: genericValue };
  }

  // 4. All validations failed
  console.error('Webhook validation failed:', genericError);
  return { error: new Error('Unsupported or invalid webhook payload') };
};
