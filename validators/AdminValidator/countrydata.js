const Joi = require('joi');

exports.countrySchema = Joi.object({
  name: Joi.string().required(),
  iso3: Joi.string().required(),
  short_name: Joi.string().required(),
  phonecode: Joi.string().required(),
  capital: Joi.string().required(),
  currency: Joi.string().required(),
  native: Joi.string().required(),
  region: Joi.string().required(),
  subregion: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);

exports.stateSchema = Joi.object({
  state_name: Joi.string().required(),
  country_id: Joi.number().required(),

  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.citySchema = Joi.object({
  city_name: Joi.string().required(),
  state_id: Joi.number().required(),
  country_id: Joi.number().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
