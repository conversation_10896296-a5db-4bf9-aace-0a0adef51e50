const Joi = require('joi');

exports.companeySchema = Joi.object({
  company_name: Joi.string().required(),
  company_address: Joi.string().required(),
  company_phone: Joi.string().required(),
  company_email: Joi.string().email().required(),
  company_website: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.masterSkillSchema = Joi.object({
  skill_name: Joi.string().required(),
  description: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.subSkillSchema = Joi.object({
  sub_skill_name: Joi.string().required(),
  master_skill_id: Joi.number().required(),
  description: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.interestSchema = Joi.object({
  interest_name: Joi.string().required(),
  category: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.designationSchema = Joi.object({
  name: Joi.string().required(),
  description: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.languageSchema = Joi.object({
  language_name: Joi.string().required(),
  language_code: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.positionSchema = Joi.object({
  title: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
