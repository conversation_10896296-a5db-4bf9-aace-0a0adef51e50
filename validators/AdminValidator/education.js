const Joi = require('joi');

exports.institudeSchema = Joi.object({
  institution_name: Joi.string().required(),
  institution_type: Joi.string().required(),
  country: Joi.string().required(),
  state_province: Joi.string().required(),
  city: Joi.string().required(),
  postal_code: Joi.string().optional(),
  latitude: Joi.number().precision(6).required(),
  longitude: Joi.number().precision(6).required(),
  website: Joi.string().required(),
  id: Joi.number().optional().allow(null),
}).unknown(true);
exports.degreeSchema = Joi.object({
  id: Joi.number().optional().allow(null),
  degree_name: Joi.string().required(),
  degree_short_form: Joi.string().required(),
  degree_type: Joi.string().required(),
  education_level: Joi.string().required(),
}).unknown(true);
exports.fieldOfStudySchema = Joi.object({
  id: Joi.number().optional().allow(null),
  degree_id: Joi.number().optional(),
  field_of_study: Joi.string().required(),
  duration: Joi.number().required(),
}).unknown(true);
