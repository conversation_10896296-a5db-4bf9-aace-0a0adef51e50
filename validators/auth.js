const Joi = require('joi');

exports.registerSchema = Joi.object({
  name: Joi.string().required().min(2),
  social_id: Joi.string().required().min(2),
  social_type: Joi.string().required().min(2),
  social_profile_pic: Joi.string().allow(''),
  email: Joi.string().email().required(),
  device_token: Joi.string().required(),
  fcm_token: Joi.string().required(),
  voip_token: Joi.string().optional().allow(''),
});

exports.addSubscription = Joi.object({
  promo_code: Joi.string().required(),
});

exports.headerSchema = Joi.object({
  devicename: Joi.string().required(),
  latitude: Joi.string().required(),
  longitude: Joi.string().required(),
}).unknown(true);

exports.profileSetupSchema = Joi.object({
  full_name: Joi.string().min(2).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().max(15).allow(''),
  designation_id: Joi.number().integer().required(),
  work_experience_id: Joi.number().integer().required(),
  dob: Joi.date().less('now').required(),
  gender: Joi.string().valid('Male', 'Female', 'Other').required(),
  about: Joi.string().min(2).allow(''),
  promo_code: Joi.string().max(15).allow(''),
  country_code: Joi.string()
    .pattern(/^\+?\d+$/)
    .optional()
    .allow(''),
});

exports.addSkillsSchema = Joi.object({
  skills: Joi.string()
    .min(1)
    .required()
    .custom((value, helpers) => {
      const ids = value.split(',');
      const uniqueIds = new Set(ids);
      if (
        uniqueIds.size !== ids.length ||
        value.startsWith(',') ||
        value.endsWith(',')
      ) {
        return helpers.message(
          'Skills must not contain duplicate IDs and a comma at the beginning or end',
        );
      }
      return value;
    }, 'Unique IDs validation'),
});

exports.skillsLookingForSchema = Joi.object({
  skills: Joi.string()
    .min(1)
    .required()
    .custom((value, helpers) => {
      const ids = value.split(',');
      const uniqueIds = new Set(ids);
      if (
        uniqueIds.size !== ids.length ||
        value.startsWith(',') ||
        value.endsWith(',')
      ) {
        return helpers.message(
          'Skills must not contain duplicate IDs and a comma at the beginning or end',
        );
      }
      return value;
    }, 'Unique IDs validation'),
});

exports.sendOtpSchema = Joi.object({
  phone_num: Joi.string()
    .pattern(/^[0-9]{7,15}$/)
    .required(),
  country_code: Joi.string()
    .pattern(/^\+\d{1,3}$/)
    .required(),
});

exports.verifyOtpSchema = Joi.object({
  otp: Joi.string().required().min(4).max(4),
  phone_num: Joi.string()
    .pattern(/^[0-9]{7,15}$/)
    .required(),
  country_code: Joi.string()
    .pattern(/^\+\d{1,3}$/)
    .required(),
});

exports.addProfilePicSchema = Joi.object({
  profile_pic: Joi.string().required().min(5),
  media_source: Joi.string().required(),
  media_score: Joi.number(),
});

exports.addProfilePitchSchema = Joi.object({
  media_path: Joi.string().required().min(5),
  media_source: Joi.string().required(),
  pitch_script: Joi.boolean().required(),
  light_space: Joi.boolean().required(),
  dress: Joi.boolean().required(),
  length: Joi.number().integer().required(),
  size: Joi.number().required(),
});

exports.getSkillsSchema = Joi.object({
  skill_id: Joi.number(),
  search: Joi.string(),
});

exports.getLookingForSchema = Joi.object({
  profile_pitch_video: Joi.boolean().required(),
  lookingfor: Joi.string().required().min(1),
  device_token: Joi.string().required(),
  fcm_token: Joi.string().required(),
  designation_id: Joi.number().required(),
  work_exp_id: Joi.number().required(),
  purpose: Joi.string().required().allow(''),
  voip_token: Joi.string().optional().allow(''),
});

exports.addAboutSchema = Joi.object({
  about: Joi.string().required().min(1),
});

exports.addOtherSkillSchema = Joi.object({
  skill_name: Joi.string().required(),
  master_skill_id: Joi.number().required(),
  skill_for: Joi.string().valid('looking', 'skill').required(),
});

exports.removeOtherSkillSchema = Joi.object({
  skill_id: Joi.number().required(),
});

exports.deleteAccount = Joi.object({
  UID: Joi.string().required(),
});
