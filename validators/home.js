const Joi = require('joi');

exports.likeSchema = Joi.object({
  other_user_id: Joi.number().required().min(1),
  super_like: Joi.boolean(),
  like: Joi.boolean(),
});

exports.settingsConfigSchema = Joi.object({
  max_distance: Joi.number().required(),
  age_range_from: Joi.number().required(),
  age_range_to: Joi.number().required(),
});

exports.nearBySchema = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
});

exports.likelistSchema = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
});

exports.MapSchema = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
  latitude: Joi.string().required(),
  longitude: Joi.string().required(),
});

exports.MatchSchema = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
  latitude: Joi.string().required(),
  longitude: Joi.string().required(),
});

exports.topUsers = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
  search: Joi.string(),
});

exports.rateUser = Joi.object({
  other_user_id: Joi.number().required().min(1),
  skill: Joi.number().optional().allow(''),
  professionalism: Joi.number().optional().allow(''),
  pitch: Joi.number().optional().allow(''),
  communication: Joi.number().optional().allow(''),
  bihavior: Joi.number().optional().allow(''),
  comments: Joi.string().optional().allow(''),
});

exports.purposeAndStatusOfMessage = Joi.object({
  purpose: Joi.string().required().allow(''),
  send_with_message: Joi.string().valid('1', '0').allow(''),
});
