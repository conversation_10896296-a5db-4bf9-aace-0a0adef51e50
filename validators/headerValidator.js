const { executeQuery } = require('../config/dbConfig');

const atob = (str) => Buffer.from(str, 'base64').toString('utf-8');

const decodeAndClean = async (value) => {
  if (!value) return null;
  try {
    const decoded = atob(value.trim());
    return decoded.slice(5, -5); // remove first 5 and last 5 chars
  } catch (err) {
    return null; // invalid base64
  }
};

const headerValidator = async (req, res, next) => {
  try {
    const headers = req.headers;

    // Decode and clean each expected header
    const devicename = await decodeAndClean(headers.devicename);
    const latitude = await decodeAndClean(headers.latitude);
    const longitude = await decodeAndClean(headers.longitude);
    const apikey = await decodeAndClean(headers.apikey);
    const version = await decodeAndClean(headers.version);
    const ip = await decodeAndClean(headers.ip);
    const address = await decodeAndClean(headers.address);

    // Allow admin API paths to skip checks
    if (
      req.path.includes('adminApi') ||
      req.path.includes('webSite') ||
      req.path.includes('subscription-webhook')
    ) {
      return next();
    }
    // Validate headers
    if (
      !devicename ||
      !latitude ||
      !longitude ||
      !apikey ||
      !version ||
      !ip ||
      !address
    ) {
      return res.status(400).json({
        status: false,
        message:
          'Missing or invalid base64 headers: devicename, latitude, longitude, apikey, version, ip, address.',
      });
    }

    // Validate API key from DB
    const check = await executeQuery(
      'SELECT id FROM authentication_keys WHERE api_key = ? AND status_id = 1',
      [apikey],
    );
    console.log('Decoded Headers:', check, apikey);
    if (!check || check.length === 0) {
      return res.status(401).json({
        status: false,
        message: 'Invalid or unauthorized API key.',
      });
    }

    // Merge cleaned headers without removing existing ones
    Object.assign(req.headers, {
      devicename,
      latitude,
      longitude,
      apikey,
      version,
      ip,
      address,
    });

    next();
  } catch (err) {
    console.error('Header validation error:', err);
    res.status(500).json({
      status: false,
      message: 'Internal server error during header validation.',
    });
  }
};

module.exports = headerValidator;
