const Joi = require('joi');

exports.ProfileSettingSchema = Joi.object({
  id: Joi.number().required(),
  swapping_in_location: Joi.boolean().required(),
  profile_has_pitch_video: Joi.boolean().required(),
  max_distance: Joi.number().integer().required(),
  age_range_from: Joi.number().integer().required(),
  age_range_to: Joi.number().integer().required(),
  show_me_on_techbuddy: Joi.boolean().required(),
  dont_show_age: Joi.boolean().required(),
  make_distance_invisible: Joi.boolean().required(),
  show_distance_in: Joi.string().valid('Km', 'Mi').required(),
  looking_up_skill_ids: Joi.string().required(),
  designation_id: Joi.number().integer().required(),
  work_experience_id: Joi.number().integer().required(),
  gender: Joi.string().valid('Male', 'Female', 'No Preferences').required(),
  location_enabled: Joi.boolean().required(),
  navigation_enabled: Joi.boolean().required(),
  short_by: Joi.string().valid('percentage', 'distance', 'both').required(),
  purpose: Joi.string().required().allow(''),
  send_with_message: Joi.string().valid('1', '0').required(),
});

exports.userLocationSchema = Joi.object({
  name: Joi.string().max(255).required(),
  latitude: Joi.number().precision(6).required(),
  longitude: Joi.number().precision(6).required(),
});

exports.userSessionSchema = Joi.object({
  device_token: Joi.string().required(),
  version: Joi.string().required(),
  ip: Joi.string().required(),
  fcm_token: Joi.string().required(),
});

exports.verifySchema = Joi.object({
  media_url: Joi.string().required().min(3),
});

exports.addEducation = Joi.object({
  institution_id: Joi.number(),
  institution_name: Joi.string(),
  degree_id: Joi.number(),
  degree_name: Joi.string(),
  field_of_study_id: Joi.number(),
  field_of_study_name: Joi.string(),
  start_date: Joi.date(),
  end_date: Joi.date(),
  grade: Joi.string(),
  description: Joi.string(),
  is_current: Joi.number().valid(0, 1),
}).or('institution_id', 'institution_name');

exports.updateEducation = Joi.object({
  education_id: Joi.number(),
  institution_id: Joi.number(),
  institution_name: Joi.string(),
  degree_id: Joi.number(),
  degree_name: Joi.string(),
  field_of_study_id: Joi.number(),
  field_of_study_name: Joi.string(),
  start_date: Joi.date(),
  end_date: Joi.date(),
  grade: Joi.string(),
  description: Joi.string(),
  is_current: Joi.number().valid(0, 1),
}).or('institution_id', 'institution_name');

exports.deleteEducation = Joi.object({
  education_id: Joi.number().required(),
});

exports.getInterests = Joi.object({
  search: Joi.string(),
});

exports.addInterests = Joi.object({
  interest_ids: Joi.string()
    .min(1)
    .required()
    .custom((value, helpers) => {
      const ids = value.split(',');
      const uniqueIds = new Set(ids);
      if (
        uniqueIds.size !== ids.length ||
        value.startsWith(',') ||
        value.endsWith(',')
      ) {
        return helpers.message(
          'Interest  must not contain duplicate IDs and a comma at the beginning or end',
        );
      }
      return value;
    }, 'Unique IDs validation'),
});

exports.addOtherInterest = Joi.object({
  interest_name: Joi.string().required().min(1),
});

exports.deleteOtherInterest = Joi.object({
  id: Joi.number().required().min(1),
});
exports.addExperience = Joi.object({
  company_id: Joi.number(),
  company_name: Joi.string(),
  employment_type_id: Joi.number().required(),
  position_id: Joi.number(),
  position_name: Joi.string(),
  location_name: Joi.string(),
  location_type_id: Joi.number(),
  start_date: Joi.date().required(),
  end_date: Joi.date(),
  description: Joi.string(),
  is_current: Joi.number().valid(1),
  latitude: Joi.number(),
  longitude: Joi.number(),
})
  .or('company_id', 'company_name')
  .or('position_id', 'position_name')
  .or('is_current', 'end_date');

exports.updateExperience = Joi.object({
  company_id: Joi.number(),
  experience_id: Joi.number(),
  company_name: Joi.string(),
  employment_type_id: Joi.number(),
  position_id: Joi.number(),
  position_name: Joi.string(),
  location_name: Joi.string(),
  location_type_id: Joi.number(),
  start_date: Joi.date().required(),
  end_date: Joi.date(),
  description: Joi.string(),
  is_current: Joi.number().valid(1),
  latitude: Joi.number(),
  longitude: Joi.number(),
})
  .or('company_id', 'company_name')
  .or('position_id', 'position_name')
  .or('is_current', 'end_date');

exports.deleteExperience = Joi.object({
  experience_id: Joi.number().required(),
});

exports.addLanguage = Joi.object({
  language_id: Joi.number().required(),
  proficiency_id: Joi.number().required(),
});

exports.updateLanguage = Joi.object({
  user_language_id: Joi.number().required(),
  language_id: Joi.number(),
  proficiency_id: Joi.number(),
});
exports.deleteLanguage = Joi.object({
  user_language_id: Joi.number().required(),
});

let service = Joi.object().keys({
  experience_id: Joi.number().required(),
  order_id: Joi.number().required(),
});

exports.updateSequance = Joi.array().items(service);

exports.addLinks = Joi.object({
  link: Joi.string().uri().required(),
  link_type: Joi.number().required(),
});
exports.deleteLinks = Joi.object({
  link_id: Joi.number().required(),
});
exports.updateLinks = Joi.object({
  link_id: Joi.number().required(),
  link: Joi.string().uri().required(),
  link_type: Joi.number().required(),
});
exports.listMedia = Joi.object({
  type: Joi.string().required(),
});
exports.updateMedia = Joi.object({
  media_id: Joi.number().required(),
  type: Joi.string().valid('Profile_Pitch', 'Profile_Pic').required(),
});

exports.syncGithub = Joi.object({
  code: Joi.string().required().messages({
    'string.base': 'Unique Code must be a string',
    'any.required': 'Unique Code is required',
  }),
  githubProfile: Joi.object({
    id: Joi.number().required().messages({
      'number.base': 'GitHub ID must be a number',
      'any.required': 'GitHub ID is required',
    }),
    login: Joi.string().required().messages({
      'string.base': 'Login must be a string',
      'any.required': 'Login is required',
    }),
    name: Joi.string().required().messages({
      'string.base': 'Name must be a string',
      'any.required': 'Name is required',
    }),
    node_id: Joi.string().allow(null, ''),
    avatar_url: Joi.string().allow(null, ''),
    bio: Joi.string().allow(null, ''),
    blog: Joi.string().allow(null, ''),
    company: Joi.string().allow(null, ''),
    created_at: Joi.date().iso().allow(null, ''),
    email: Joi.string().email().allow(null, ''),
    followers: Joi.number().integer().min(0).allow(null),
    following: Joi.number().integer().min(0).allow(null),
    public_gists: Joi.number().integer().min(0).allow(null),
    public_repos: Joi.number().integer().min(0).allow(null),
    hireable: Joi.boolean().allow(null),
    location: Joi.string().allow(null, ''),
    html_url: Joi.string().allow(null, ''),
    twitter_username: Joi.string().allow(null, ''),
    type: Joi.string().allow(null, ''),
    site_admin: Joi.boolean().allow(null),
    updated_at: Joi.date().iso().allow(null, ''),
    url: Joi.string().allow(null, ''),
    organizations_url: Joi.string().allow(null, ''),
    repos_url: Joi.string().allow(null, ''),
    events_url: Joi.string().allow(null, ''),
    received_events_url: Joi.string().allow(null, ''),
    starred_url: Joi.string().allow(null, ''),
    subscriptions_url: Joi.string().allow(null, ''),
    gists_url: Joi.string().allow(null, ''),
    followers_url: Joi.string().allow(null, ''),
    following_url: Joi.string().allow(null, ''),
    gravatar_id: Joi.string().allow(null, ''),
    notification_email: Joi.string().email().allow(null, ''),
    user_view_type: Joi.string().allow(null, ''),
  }).required(),
});

exports.getProfileDetailsList = Joi.object({
  ids: Joi.string()
    .min(1)
    .required()
    .custom((value, helpers) => {
      // Split by comma and validate each ID
      const idArray = value.split(',').map((id) => id.trim());

      // Check for empty values
      if (idArray.some((id) => id === '')) {
        return helpers.message('IDs must not contain empty values');
      }

      // Check if all values are valid positive integers
      const invalidIds = idArray.filter(
        (id) => !/^\d+$/.test(id) || parseInt(id, 10) <= 0,
      );
      if (invalidIds.length > 0) {
        return helpers.message('All IDs must be positive integers');
      }

      // Check for duplicates
      const uniqueIds = new Set(idArray);
      if (uniqueIds.size !== idArray.length) {
        return helpers.message('IDs must not contain duplicates');
      }

      return value;
    }, 'Comma-separated IDs validation'),
});
