const Joi = require('joi');

exports.notificationlist = Joi.object({
  page_size: Joi.number().required(),
  page_number: Joi.number().required(),
});

// exports.notificationRead = Joi.object({
//   notification_id: Joi.number().required(),
// });
exports.notificationDelete = Joi.object({
  notification_ids: Joi.string().required(),
});
exports.chatNotification = Joi.object({
  other_user_id: Joi.number().required(),
  message: Joi.string().required(),
  type: Joi.string().required(),
});
