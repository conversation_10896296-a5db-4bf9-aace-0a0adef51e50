const { executeQuery, response } = require('../config/dbConfig');
const {
  // combineSkills,
  insertAction,
  getTemplates,
  globalVar,
  getUserData,
  getUserFcm,
  combineSkills,
} = require('../utils');
const { notification } = require('../utils/firebase');
// const { getAlgoliaUsers } = require('../utils/algoliaSearch');

exports.likeUser = async (req, res, next) => {
  try {
    const { other_user_id, like, super_like } = req.body;
    const userId = req.userData.id;
    const { devicename, latitude, longitude, ip } = req.headers;
    // const activity_id = await insertAction(req, like ? 'Like': (super_like?'Superlike':'Dislike'))
    const data = await executeQuery('CALL LikeUser(?,?,?,?,?,?,?,?)', [
      userId,
      other_user_id,
      like,
      super_like,
      devicename,
      latitude,
      longitude,
      ip,
    ]);
    if (data) {
      if (data[0].length && data[0][0].match_id > 0) {
        const userData = await getUserData(userId);
        const likeTemp = await getTemplates('Match');
        const title = globalVar.replaceTextValue(likeTemp[0].title, [
          userData[0].name,
        ]);
        const fcmData = await getUserFcm(other_user_id);
        if (fcmData.length) {
          try {
            await notification(
              title,
              likeTemp[0].contant,
              fcmData,
              {
                match_id: data[0][0].match_id.toString(),
                sender_id: userId.toString(),
                sender_name: userData[0].name,
                type: likeTemp[0].module,
                sender_profile_pic: userData[0].profile_pic,
              },
              likeTemp[0].module,
            );
          } catch (notificationError) {
            console.warn(
              'Failed to send match notification:',
              notificationError.message,
            );
            // Continue execution even if notification fails
          }
        }
      } else {
        if (like || super_like) {
          const userData = await getUserData(userId);
          const likeTemp = await getTemplates(like ? 'Like' : 'SuperLike');

          const title = globalVar.replaceTextValue(likeTemp[0].title, [
            userData[0].name,
          ]);
          const fcmData = await getUserFcm(other_user_id);
          if (fcmData.length) {
            try {
              await notification(
                title,
                likeTemp[0].contant,
                fcmData,
                {
                  sender_id: userId.toString(),
                  sender_name: userData[0].name,
                  type: likeTemp[0].module,
                  sender_profile_pic: userData[0].profile_pic,
                },
                likeTemp[0].module,
              );
            } catch (notificationError) {
              console.warn(
                'Failed to send like notification:',
                notificationError.message,
              );
              // Continue execution even if notification fails
            }
          }
        }
      }
      response(
        res,
        201,
        'Like  User successfully.',
        data[0].length ? data[0][0] : null,
        true,
      ); // 201 Created
    } else {
      response(res, 409, 'Failed to like user', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.showUserLikes = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { page_number, page_size } = req.body;

    const data = await executeQuery('CALL showUserLikes(?,?,?)', [
      userId,
      page_number,
      page_size,
    ]);
    response(
      res,
      200,
      'Users  List Fetched Successfully',
      {
        like_list: data[0],
        total_pages: data[0][0]?.total_pages || 0,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.matchUser = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { other_user_id } = req.body;

    const data = await executeQuery('CALL showMatchUser(?,?)', [
      userId,
      other_user_id,
    ]);
    response(res, 200, 'Users  List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.showMatchUsers = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const data = await executeQuery('CALL showUserMatches(?)', [userId]);
    response(res, 200, 'Match Users  List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.updateSettings = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { max_distance, age_range_from, age_range_to } = req.body;
    const activity_id = await insertAction(req, 'Update profile');

    const data = await executeQuery('CALL UpdateUserSetting(?,?,?,?,?)', [
      max_distance,
      age_range_from,
      age_range_to,
      userId,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Settings Updated Successfully', {}, true); // 201 Created
    } else {
      response(res, 409, 'Failed to Update Settings', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.nearBy = async (req, res, next) => {
  try {
    const { page_size, page_number } = req.body;
    const userId = req.userData.id;
    const data = await executeQuery('call NearBy1(?,?,?);', [
      userId,
      page_number,
      page_size,
    ]);

    response(
      res,
      200,
      'Fetched  Successfully ',
      {
        near_by_users: combineSkills(data[0]),
        user_ids: data[1][0].sorted_user_ids || [],
        total_pages: data[2][0].total_pages || 0,
      },

      true,
    ); // 201 Created
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.bestMatches = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { page_size, page_number, latitude, longitude } = req.body;
    const data = await executeQuery('CALL BestMachesUsers(?,?,?,?,?)', [
      userId,
      page_number,
      page_size,
      latitude,
      longitude,
    ]);
    response(
      res,
      200,
      'Match Users  List Fetched Successfully',
      {
        best_match: data[0].slice(0, 3),
        match_List: data[0].slice(3),
        total_pages: data[1][0]?.total_pages || 0,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.bestMatchesWithMap = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { latitude, longitude, page_size, page_number } = req.body;
    const data = await executeQuery('CALL BestMachesUsers(?,?,?,?,?)', [
      userId,
      page_number,
      page_size,
      latitude,
      longitude,
    ]);
    response(
      res,
      200,
      'Match Users  List Fetched Successfully',
      {
        // best_match: data[0].slice(0, 3),
        match_List: data[0],
        total_pages: data[1][0]?.total_pages || 0,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.topUsers = async (req, res, next) => {
  try {
    const { search, page_number, page_size } = req.body;

    const data = await executeQuery('CALL TopUsers(?,?,?,?)', [
      req.userData.id,
      page_number,
      page_size,
      search,
    ]);

    const users = data[0] || [];
    const pageInfo =
      Array.isArray(data[1]) && data[1].length === 1
        ? data[1][0]
        : { total_pages: 0 };

    response(
      res,
      200,
      'Match Users List Fetched Successfully',
      {
        records: users,
        ...pageInfo,
      },
      true,
    );
  } catch (error) {
    console.error('TopUsers error:', error);
    next(error);
  }
};

exports.rateUser = async (req, res, next) => {
  try {
    const {
      other_user_id,
      skill,
      professionalism,
      pitch,
      communication,
      bihavior,
      comments,
    } = req.body;
    const userId = req.userData.id;
    // const { devicename, latitude, longitude, ip } = req.headers;
    // const activity_id = await insertAction(req, like ? 'Like': (super_like?'Superlike':'Dislike'))
    const data = await executeQuery('CALL RateUser(?,?,?,?,?,?,?,?)', [
      userId,
      other_user_id,
      skill || 0,
      professionalism || 0,
      pitch || 0,
      communication || 0,
      bihavior || 0,
      comments || '',
    ]);
    console.log(data, 'dddd');
    if (data) {
      response(res, 201, 'Rate  User successfully.', {}, true); // 201 Created
    } else {
      response(res, 409, 'Failed to rate user', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.GetSubscriptionDetailForHome = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const data = await executeQuery('call GetSubscriptionDetailForHome(?);', [
      userId,
    ]);

    response(
      res,
      200,
      'Fetched  Successfully ',
      {
        subscription: data[0][0],
        ...data[1][0],
        ...data[2][0],
      },

      true,
    ); // 201 Created
  } catch (error) {
    console.log(error);
    next(error);
  }
};

exports.getPurposeAndStatusOfMessage = async (req, res, next) => {
  try {
    const { purpose, send_with_message } = req.body;
    const data = await executeQuery(
      'call GetPurposeAndStatusOfMessage(?,?,?);',
      [purpose, send_with_message, req.userData.id],
    );
    response(res, 200, 'Fetched  Successfully ', data[0][0], true); // 201 Created
  } catch (error) {
    console.log(error);
    next(error);
  }
};
