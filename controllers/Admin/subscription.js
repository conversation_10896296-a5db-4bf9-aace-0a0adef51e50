const { executeQuery, response } = require('../../config/dbConfig');

exports.GetSubscriptionList = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL GetSubscriptionList(?,?,?)', [
      pageNumber,
      pagesize,
      search,
    ]);
    response(
      res,
      200,
      'Subscription List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.AddEditSubscription = async (req, res, next) => {
  try {
    const {
      id = null,
      plan_name,
      price,
      currency,
      validity_days,
      trial_days,
      google_product_id,
      apple_product_id,
      description,
      status,
    } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateSubscriptionPlan(?,?,?,?,?,?,?,?,?,?);',
      [
        id,
        plan_name,
        price,
        currency,
        validity_days,
        trial_days,
        google_product_id,
        apple_product_id,
        description,
        status,
      ],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Subscription updated successfully.'
            : 'Subscription added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Field.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.GetFeatureList = async (req, res, next) => {
  try {
    const { feature_id, search } = req.body;
    const data = await executeQuery('CALL GetFeatureList(?,?)', [
      search,
      feature_id,
    ]);

    response(
      res,
      200,
      'Subscription List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
        featureList: data[2],
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.AddFeatureSubscription = async (req, res, next) => {
  try {
    const { id, feature_id, subscription_plan_id } = req.body;

    const data = await executeQuery('CALL AddFeatureSubscription(?,?,?)', [
      id,
      feature_id,
      subscription_plan_id,
    ]);

    if (data.affectedRows) {
      response(
        res,
        201,
        id > 0
          ? 'Subscription Remove successfully.'
          : 'Subscription Added successfully.',
        {},
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to add Field.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.AddFeatures = async (req, res, next) => {
  try {
    const { id, feature_name, description } = req.body;

    const data = await executeQuery('CALL AddEditFeatures(?,?,?)', [
      id,
      feature_name,
      description,
    ]);
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      console.log(data, 'data');
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Feature Update successfully.'
            : 'Feature Added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Field.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.deleteSubscription = async (req, res, next) => {
  try {
    const { id } = req.body;
    // const data = await executeQuery('call DeleteMessageList(?)',[notification_id]);
    const data = await executeQuery('CALL DeleteSubscription(?);', [id]);
    if (data[0][0].affected_rows) {
      response(res, 201, 'Deleted successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetAllFeatureList = async (req, res, next) => {
  try {
    const { search } = req.body;
    const data = await executeQuery('CALL GetAllFeatureList(?)', [search]);

    response(
      res,
      200,
      'Subscription List Fetched Successfully',
      {
        data: data[0],
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.DeleteFeature = async (req, res, next) => {
  try {
    const { id, type } = req.body;
    // const data = await executeQuery('call DeleteMessageList(?)',[notification_id]);
    const data = await executeQuery('CALL DeleteFeature(?,?);', [id, type]);
    console.log(data, 'data');
    if (data[0][0].affected_count) {
      response(
        res,
        201,
        `${type.charAt(0).toUpperCase() + type.slice(1)} successfully.`,
        {},
        true,
      );
    } else {
      response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
