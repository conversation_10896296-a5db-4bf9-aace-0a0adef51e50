const { executeQuery, response } = require('../../config/dbConfig');

exports.addCompanies = async (req, res, next) => {
  try {
    const {
      id = null,
      company_name,
      company_address,
      company_phone,
      company_email,
      company_website,
    } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateCompaniesByAdmin(?,?,?,?,?,?);',
      [
        id,
        company_name,
        company_address,
        company_phone,
        company_email,
        company_website,
      ],
    );

    console.log(data[0], 'data[0]', data.affectedRows);
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Company updated successfully.'
            : 'Company added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Institude.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.GetCompanies = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);
    const data = await executeQuery('CALL listCompanies(?,?,?,?,?);', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
    ]);
    if (data[0]) {
      response(
        res,
        200,
        'Master skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to add Master.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetDesignation = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery('CALL listDesignation(?,?,?,?,?);', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
    ]);
    if (data[0]) {
      response(
        res,
        200,
        'Master skill fetched successfully.',
        {
          data: isMaster ? data[0] : [],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to add Master.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.GetTotalRecords = async (req, res, next) => {
  try {
    const { isMaster } = req.body;
    const data = await executeQuery('CALL GetExperianceRecords(?)', [isMaster]);
    response(
      res,
      200,
      'Records Fetched Successfully',
      {
        Companies: data[0][0].Company_Records,
        Designations: data[0][0].Designation_Records,
        Positions: data[0][0].Positions_Records,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addMasterSkill = async (req, res, next) => {
  try {
    const { id = null, skill_name, description } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateMasterSkillByAdmin(?,?,?);',
      [id, skill_name, description],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Master skill updated successfully.'
            : 'Master skill added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Institude.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.GetMasterSkill = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetMasterSkill();', []);

    if (data[0]) {
      response(res, 200, 'Master skill fetched successfully.', data[0], true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Master.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetMasterSkillWithpagination = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery(
      'CALL GetMasterSkillWithPagination(?,?,?,?,?);',
      [pageNumber, pagesize, search, isMaster, sortJson],
    );

    if (data[0]) {
      response(
        res,
        200,
        'Master skill fetched successfully.',
        {
          data: isMaster ? data[0] : [],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to add Master.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetSubSkillWithpagination = async (req, res, next) => {
  try {
    const {
      pageNumber,
      pagesize,
      search,
      isMaster,
      master_skill_id = null,
      sort,
    } = req.body;

    const sortJson = JSON.stringify(sort);
    const data = await executeQuery(
      'CALL GetSubSkillWithPagination(?,?,?,?,?,?);',
      [pageNumber, pagesize, search, isMaster, master_skill_id, sortJson],
    );

    if (data[0]) {
      response(
        res,
        200,
        'Master skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to add Master.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.GetSkillTotalRecords = async (req, res, next) => {
  try {
    const { isMaster } = req.body;
    const data = await executeQuery('CALL GetSkillsRecords(?)', [isMaster]);
    response(
      res,
      200,
      'Records Fetched Successfully',
      {
        SubSkills: data[0][0].skill_Records,
        MasterSkills: data[0][0].master_skill_Records,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addSubSkill = async (req, res, next) => {
  try {
    const {
      id = null,
      master_skill_id,
      sub_skill_name,
      description,
    } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateSubSkillSkillByAdmin(?,?,?,?);',
      [id, master_skill_id, sub_skill_name, description],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Subskill updated successfully.'
            : 'Subskill added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add SubSkill.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.addInterest = async (req, res, next) => {
  try {
    const { id = null, interest_name, category } = req.body;

    const data = await executeQuery('CALL AddUpdateInterestByAdmin(?,?,?);', [
      id,
      interest_name,
      category,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Interest updated successfully.'
            : 'Interest added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Interest.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addDesignation = async (req, res, next) => {
  try {
    const { id = null, name, description } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateDesignationByAdmin(?,?,?);',
      [id, name, description],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Designation updated successfully.'
            : 'Designation added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add  Designation.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addLanguage = async (req, res, next) => {
  try {
    const { id = null, language_name, language_code } = req.body;

    const data = await executeQuery('CALL AddUpdateLanguageByAdmin(?,?,?);', [
      id,
      language_name,
      language_code,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Language updated successfully.'
            : 'Language added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add  Language.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.getInterest = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery('CALL listInterests(?,?,?,?,?);', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
    ]);

    if (data[0]) {
      response(
        res,
        200,
        'Countries skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          Interests: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Countries.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.getlanguages = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, sort } = req.body;
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery('CALL listLanguages(?,?,?,?);', [
      pageNumber,
      pagesize,
      search,
      sortJson,
    ]);

    if (data[0]) {
      response(
        res,
        200,
        'Countries skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          Languages: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Countries.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.addPosition = async (req, res, next) => {
  try {
    const { id = null, title } = req.body;

    const data = await executeQuery('CALL AddUpdateJobPositionsByAdmin(?,?);', [
      id,
      title,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Position updated successfully.'
            : 'Position added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add  Position.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.getPositions = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery('CALL listPositions(?,?,?,?,?);', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
    ]);
    if (data[0]) {
      response(
        res,
        200,
        'Positions fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          Positions: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Positions.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.addSkillsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, master_skill_id, sub_skill_name, description } = req.body;

    const data = await executeQuery('CALL AddMasterSkillbyAdmin(?,?,?,?);', [
      master_skill_id,
      sub_skill_name,
      description,
      ids,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, 'Skill updated successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.getAllSkills = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL getAllsubSkills()', []);
    response(res, 200, 'Skills fetched successfully.', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.assignSkillsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id } = req.body;

    const data = await executeQuery('CALL Assign_Master_skills(?,?,?);', [
      master_id,
      user_ids,
      ids,
    ]);
    console.log(data, 'dssdsdsd');
    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.getInterests = async (req, res, next) => {
  try {
    const { search } = req.query;
    const data = await executeQuery('CALL GetInterest(?)', [search]);
    response(res, 200, 'Interests List Fetched  Successfully.', data[0], true); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.addInterestsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, interest_name, category } = req.body;

    const data = await executeQuery('CALL AddMasterInterestbyAdmin(?,?,?);', [
      interest_name,
      category,
      ids,
    ]);
    console.log(data, 'fffffffdf');
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, ' Interest updated successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.assignInterestAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id } = req.body;

    const data = await executeQuery('CALL Assign_Master_interest(?,?,?);', [
      master_id,
      user_ids,
      ids,
    ]);

    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.addCompaniesAsMasterEntry = async (req, res, next) => {
  try {
    const {
      ids,
      company_name,
      company_address,
      company_phone,
      company_email,
      company_website,
    } = req.body;

    const data = await executeQuery(
      'CALL AddMasterCompaniesbyAdmin(?,?,?,?,?,?);',
      [
        company_name,
        company_address,
        company_phone,
        company_email,
        company_website,
        ids,
      ],
    );

    console.log(data[0], 'data[0]', data.affectedRows);
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, 'Company added successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Institude.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.addPositionsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, title } = req.body;

    const data = await executeQuery('CALL AddMasterJobPositionsbyAdmin(?,?);', [
      title,
      ids,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,

          'Position added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add  Position.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.assignPositionsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id, experiance_ids } = req.body;

    const data = await executeQuery('CALL Assign_Master_Positions(?,?,?,?);', [
      master_id,
      user_ids,
      ids,
      experiance_ids,
    ]);

    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.getUserRole = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetUserRole(?)', [search]);
    response(res, 200, 'User role List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.getCompaniesList = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetCompaniesList(?)', [search]);
    response(res, 200, 'Companies List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.assignComapniesAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id, experiance_ids } = req.body;

    const data = await executeQuery('CALL Assign_Master_Comapies(?,?,?,?);', [
      master_id,
      user_ids,
      ids,
      experiance_ids,
    ]);

    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
