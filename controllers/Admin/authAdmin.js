const { executeQuery, response } = require('../../config/dbConfig');
const educationData = require('../../config/dbOtherEdit.json');
const mailer = require('../../config/mailerConfig');

const { generateToken } = require('../../utils/jwtUtils');
const dbTableData = require('../../config/dbTableConfig.json');

const bcrypt = require('bcrypt');
const { mailOptions } = require('../../utils/mailhelpers');
const moment = require('moment');
const AdminEmail = process.env.NODE_MAIL_USER;
const isOtp = process.env.IS_OTP_ENTERD;

exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;
    const existEmail = await executeQuery('CALL GetAdminDetail(?)', [email]);

    if (existEmail[0].length === 0) {
      return response(
        res,
        400,
        'The email address you entered is incorrect',
        {},
        false,
      );
    }

    const { password_hash } = existEmail[0][0];
    const isValidPassword = await bcrypt.compare(password, password_hash);

    if (!isValidPassword) {
      return response(res, 400, 'Invalid email or password', {}, false);
    }

    // const authToken = await generateToken(id);
    console.log(isOtp, isOtp == true);
    // If OTP flow is enabled
    if (isOtp == 'true') {
      const otp = Math.floor(1000 + Math.random() * 9000);
      const expireOtp = moment()
        .add(5, 'minutes')
        .format('YYYY-MM-DD HH:mm:ss');

      await executeQuery('CALL InsertAdminOtp(?,?,?)', [
        AdminEmail,
        otp,
        expireOtp,
      ]);

      await mailer.sendMail(mailOptions(otp, AdminEmail), async (err, info) => {
        if (err) {
          console.error('Error sending mail:', err);
        }
        console.log(info, 'mail status');
      });
      response(res, 200, 'OTP sent successfully', {}, true);
    } else {
      return response(res, 200, 'Login Successfully', {}, true);
    }
  } catch (error) {
    next(error);
  }
};

exports.deleteMasterEntry = async (req, res, next) => {
  try {
    const { id, master_type, table_key, isMaster } = req.body;

    console.log(isMaster, 'dfdfdfd');
    if (isMaster == 0) {
      // console.log(tableName, 'tableName122');

      const key = educationData[master_type][table_key];
      const { table } = key;
      const data1 = await executeQuery('CALL deleteOtherUserData(?,?)', [
        id,
        table,
      ]);

      if (data1[0][0].affected_rows) {
        response(res, 201, 'Deleted successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
      }
    } else {
      const tableName = dbTableData?.[master_type]?.[table_key];

      console.log(tableName, 'tableName');
      const data = await executeQuery('CALL DeleteMasterEntries(?,?);', [
        id,
        tableName,
      ]);

      console.log('data[0]', data[0][0]);

      if (data[0][0].affected_rows) {
        response(res, 201, 'Deleted successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.verifyOtp = async (req, res, next) => {
  try {
    const { otp, email } = req.body;
    // const { devicename, latitude, longitude, version, ip } = req.headers;
    const existEmail = await executeQuery('CALL GetAdminDetail(?)', [email]);
    const { id } = existEmail[0][0];

    const authToken = await generateToken(id);

    if (isOtp == 'true') {
      const data = await executeQuery('CALL verifyAdminOtp(?,?)', [
        AdminEmail,
        otp,
      ]);
      if (data[0].length) {
        if (data[0][0].message == 'OTP verified successfully') {
          response(res, 200, data[0][0].message, { token: authToken }, true);
        } else {
          response(res, 400, data[0][0].message, { token: authToken }, false);
        }
      }
    } else {
      if (otp == 1234) {
        response(
          res,
          200,
          'OTP verified successfully',
          { token: authToken },
          true,
        );
      } else {
        response(res, 400, 'Invalid OTP', {}, false);
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.resendOtp = async (req, res, next) => {
  try {
    const otp = Math.floor(1000 + Math.random() * 9000);
    const expireOtp = moment().add(5, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    await executeQuery('CALL ResendOtp(?,?,?)', [AdminEmail, otp, expireOtp]);

    // await mailer.sendMail(mailOptions(otp, AdminEmail), async (err, info) => {
    //   if (err) {
    //     console.error('Error sending mail:', err);
    //   }
    //   console.log(info, 'mail status');
    // });
    response(res, 200, 'OTP sent successfully', {}, true);
  } catch (error) {
    next(error);
  }
};
