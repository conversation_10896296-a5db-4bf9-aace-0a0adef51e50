const { executeQuery, response } = require('../../config/dbConfig');
const educationData = require('../../config/dbOtherEdit.json');
exports.addInstitude = async (req, res, next) => {
  try {
    const {
      id = null,
      institution_name,
      institution_type,
      country,
      state_province,
      city,
      postal_code,
      latitude,
      longitude,
      website,
    } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateInstitudeByAdmin(?,?,?,?,?,?,?,?,?,?);',
      [
        id,
        institution_name,
        institution_type,
        country,
        state_province,
        city,
        postal_code,
        latitude,
        longitude,
        website,
      ],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Institute updated successfully.'
            : 'Institute added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Institute.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addDegree = async (req, res, next) => {
  try {
    const {
      id = null,
      degree_name,
      degree_short_form,
      degree_type,
      education_level,
    } = req.body;

    const data = await executeQuery('CALL AddUpdateDegreeByAdmin(?,?,?,?,?);', [
      id,
      degree_name,
      degree_short_form,
      degree_type,
      education_level,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Degree updated successfully.'
            : 'Degree added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addfieldOfStudy = async (req, res, next) => {
  try {
    const { id = null, degree_id, field_of_study, duration } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateFieldofStudyByAdmin(?,?,?,?);',
      [id, degree_id, field_of_study, duration],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0 ? 'Field updated successfully.' : 'Field added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Field.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.getDegreelist = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetAllDegrees(?)', [search]);
    response(res, 200, 'Degree List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.getDegreelistWithPagination = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;

    // Convert `sort` object to JSON string to send to MySQL stored procedure
    const sortJson = JSON.stringify(sort);

    const data = await executeQuery(
      'CALL GetAllDegreesPaginamtion(?,?,?,?,?)',
      [
        pageNumber,
        pagesize,
        search,
        isMaster,
        sortJson, // send sort as JSON string
      ],
    );
    response(
      res,
      200,
      'Degree List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getFieldOfStudy = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort,degress_id } = req.body;
    const sortJson = JSON.stringify(sort);
    const data = await executeQuery('CALL GetFieldOfStudypage(?,?,?,?,?,?)', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
      degress_id
    ]);
    response(
      res,
      200,
      'Degree List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.GetInstitudes = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search, isMaster, sort } = req.body;
    const sortJson = JSON.stringify(sort);
    const data = await executeQuery('CALL GetInstitudesWithpage(?,?,?,?,?)', [
      pageNumber,
      pagesize,
      search,
      isMaster,
      sortJson,
    ]);
    response(
      res,
      200,
      'Institudes List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.GetTotalRecords = async (req, res, next) => {
  try {
    let { isMaster } = req.body;
    const data = await executeQuery('CALL GetEducationRecords(?)', [isMaster]);
    response(
      res,
      200,
      'Institudes List Fetched Successfully',
      {
        Institutes: data[0][0].Institude_Records,
        FieldOfStudy: data[0][0].Fd_Records,
        Degrees: data[0][0].Dg_Records,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.editEducationData = async (req, res, next) => {
  try {
    const { id, master_type, table_key } = req.body;
    const key = educationData[master_type][table_key];
    const { table, payloadKey } = key;
    const value = req.body[payloadKey];
    console.log(value, table, payloadKey, 'sdsdsds', Array.isArray(payloadKey));
    if (Array.isArray(payloadKey)) {
      // For Skills (multi-column)
      const [col1, col2] = payloadKey;
      const val1 = req.body[col1];
      const val2 = req.body[col2];

      await executeQuery('CALL editSkillData(?,?,?,?,?,?)', [
        id,
        val1,
        val2,
        table,
        col1,
        col2,
      ]);
      response(res, 200, 'Update Successfully', {}, true); // 200 OK
    } else {
      let data = await executeQuery('CALL editEducationData(?,?,?,?)', [
        id,
        value,
        table,
        payloadKey,
      ]);

      if (data[0]) {
        const { response_message } = data[0][0];
        response(res, 400, response_message, {}, false); // 409 Conflict
      } else {
        response(res, 200, 'Update Successfully', data[0], true); // 200
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.getFieldList = async (req, res, next) => {
  try {
    const { search = null, degree_id = null } = req.query;
    const data = await executeQuery('CALL GetFieldOfStudy(?,?)', [
      search,
      degree_id,
    ]);
    response(res, 200, 'Degree List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.deleteEducationData = async (req, res, next) => {
  try {
    const { id, master_type, table_key } = req.body;
    const key = educationData[master_type][table_key];
    const { table } = key;
    await executeQuery('CALL deleteOtherUserData(?,?)', [id, table]);
    response(res, 200, 'Delete Successfully', {}, true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addDegreeAsMasterEntry = async (req, res, next) => {
  try {
    const {
      ids,
      degree_name,
      degree_short_form,
      degree_type,
      education_level,
    } = req.body;

    const data = await executeQuery('CALL AddMasterDegreeByAdmin(?,?,?,?,?);', [
      degree_name,
      degree_short_form,
      degree_type,
      education_level,
      ids,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, 'Degree updated successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addFeildsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, field_of_study, duration, degree_id } = req.body;

    const data = await executeQuery('CALL AddMasterFieldsByAdmin(?,?,?,?);', [
      degree_id,
      field_of_study,
      duration,

      ids,
    ]);

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, 'Degree updated successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addInstitudesAsMasterEntry = async (req, res, next) => {
  try {
    const {
      ids,
      institution_name,
      institution_type,
      country,
      state_province,
      city,
      postal_code,
      latitude,
      longitude,
      website,
    } = req.body;

    const data = await executeQuery(
      'CALL AddMasterInstituesByAdmin(?,?,?,?,?,?,?,?,?,?);',
      [
        institution_name,
        institution_type,
        country,
        state_province,
        city,
        postal_code,
        latitude,
        longitude,
        website,
        ids,
      ],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(res, 201, 'Degree updated successfully.', {}, true); // 201 Created
      } else {
        response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.assignDegreeAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id, education_ids } = req.body;

    const data = await executeQuery('CALL Assign_Master_Degree(?,?,?,?);', [
      master_id,
      user_ids,
      ids,
      education_ids,
    ]);

    if (data.affectedRows) {
      response(res, 201, 'Degree updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.assignFieldsAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id, education_ids } = req.body;
    console.log(master_id, ids, user_ids, education_ids, 'hfgdfhgdhhfd');

    const data = await executeQuery('CALL Assign_Master_Fields(?,?,?,?);', [
      master_id,
      user_ids,
      ids,
      education_ids,
    ]);
    console.log(data, 'dssdsdsd');
    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.assignInstitudesAsMasterEntry = async (req, res, next) => {
  try {
    const { ids, user_ids, master_id, education_ids } = req.body;
    console.log(master_id, ids, user_ids, education_ids, 'hfgdfhgdhhfd');

    const data = await executeQuery('CALL Assign_Master_Institudes(?,?,?,?);', [
      master_id,
      user_ids,
      ids,
      education_ids,
    ]);
    console.log(data, 'dssdsdsd');
    if (data.affectedRows) {
      response(res, 201, 'fields updated successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add Degree.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.getInstitude = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetInstitute(?)', [search]);
    response(res, 200, 'Institude List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
