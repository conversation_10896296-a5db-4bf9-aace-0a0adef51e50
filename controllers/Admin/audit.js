const { executeQuery, response } = require('../../config/dbConfig');
const mailer = require('../../config/mailerConfig');
const { getUserData, getStatus } = require('../../utils');
const { pitchStatusMail } = require('../../utils/mailhelpers');

exports.GetProfilePicPitch = async (req, res, next) => {
  try {
    const { pageNumber, pageSize, search, media_type, media_status } = req.body;
    const data = await executeQuery('CALL listProfilePicAndPitch(?,?,?,?,?)', [
      pageNumber,
      pageSize,
      search,
      media_type,
      media_status,
    ]);
    response(
      res,
      200,
      'Media List Fetched Successfully',
      {
        data: data[0],
        ...data[1][0],
        ...data[2][0],
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.updateMediaStatus = async (req, res, next) => {
  try {
    const { media_id, media_status, id, media_type } = req.body;
    const data = await executeQuery('CALL updateMediaStatus(?,?,?,?)', [
      media_id,
      media_status,
      media_type,
      id,
    ]);
    const userData = await getUserData(id);
    const status = await getStatus(media_status);

    console.log(status);
    const { email: currentEmail, name } = userData[0];
    mailer.sendMail(
      pitchStatusMail(
        name,
        currentEmail,
        `Your pitch status is now ${status}`,
        `${status}`,
      ),
      async (err, info) => {
        if (err) {
          console.error('Error sending mail:', err);
        }
        console.log(info, 'mail status');
      },
    );
    response(res, 200, 'Media Status Updated Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
