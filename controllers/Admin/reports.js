const { executeQuery, response } = require('../../config/dbConfig');
const mailer = require('../../config/mailerConfig');
const { getUserData } = require('../../utils');
const { mailSuspend } = require('../../utils/mailhelpers');

exports.GetReportsList = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL listAllReportedUsers(?,?,?)', [
      pageNumber,
      pagesize,
      search,
    ]);
    response(
      res,
      200,
      'Reported List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.suspendUser = async (req, res, next) => {
  try {
    const { reported_id, reason } = req.body;

    await executeQuery('CALL SuspendUser(?,?)', [reported_id, reason]);
    const userData = await getUserData(reported_id);

    const { email: currentEmail, name } = userData[0];
    mailer.sendMail(
      mailSuspend(name, currentEmail, reason),
      async (err, info) => {
        if (err) {
          console.error('Error sending mail:', err);
        }
        console.log(info, 'mail status');
      },
    );

    response(res, 200, 'User Suspended Successfully', {}, true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.unSuspendUser = async (req, res, next) => {
  try {
    const { id } = req.body;

    await executeQuery('CALL unSuspendUser(?)', [id]);
    // const userData = await getUserData(id);

    // const { email: currentEmail, name } = userData[0];
    // mailer.sendMail(
    //   mailSuspend(name, currentEmail, reason),
    //   async (err, info) => {
    //     if (err) {
    //       console.error('Error sending mail:', err);
    //     }
    //     console.log(info, 'mail status');
    //   },
    // );

    response(res, 200, 'Active Successfully', {}, true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.GetContactList = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL listAllContactUsData(?,?,?)', [
      pageNumber,
      pagesize,
      search,
    ]);
    response(
      res,
      200,
      'Contact form data List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
