const { executeQuery, response } = require("../../config/dbConfig");

exports.getErrorLogs = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;

    const data = await executeQuery('CALL GetErrorLogsList(?,?,?)', [
      pageNumber,
      pagesize,
      search 
    ]);
    response(
      res,
      200,
      'Error Logs List Fetched Successfully',
      {
        data: data[0],
        total_pages: data[1][0].total_pages,
        totalRecords: data[1][0].totalRecords,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.updateResolveStatus = async (req, res, next) => {
  try {
    const { id, remarks } = req.body;

 await executeQuery('CALL updateResoleStatus(?,?)', [
      id,
      remarks
    ]);
    response(
      res,
      200,
      'Update resolve status Successfully',
      {},
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.deleteErrorLogs = async (req, res, next) => {
  try {
    const { id } = req.query;
console.log(id,'hfgdhdg');
    if (!id) {
      return response(res, 400, 'ID is required', {}, false); // 400 Bad Request
    }
 await executeQuery('CALL deleteErrorLogs(?)', [
      id
    ]);
    response(
      res,
      200,
      'Update resolve status Successfully',
      {},
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};