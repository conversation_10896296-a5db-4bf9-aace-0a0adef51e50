const { executeQuery, response } = require('../../config/dbConfig');

exports.AddEditPromoCode = async (req, res, next) => {
  try {
    const {
      id = null,
      code,
      discount_type,
      discount_value,
      usage_type,
      max_usage,
      max_usage_per_user,
      subscription_id,
      validity_days,
      valid_from,
      valid_to,
    } = req.body;
    const added_by = req.userData.id;

    const data = await executeQuery(
      'CALL AddUpdatePromoCodeByAdmin(?,?,?,?,?,?,?,?,?,?,?,?);',
      [
        id,
        added_by,
        code,
        discount_type,
        discount_value,
        usage_type,
        max_usage,
        max_usage_per_user,
        subscription_id,
        validity_days,
        valid_from,
        valid_to,
      ],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Promo code updated successfully.'
            : 'Promo code added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Field.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.getPromoCodes = async (req, res, next) => {
  const { pageNumber, pagesize, search } = req.body;
  try {
    const usersList = await executeQuery('call listPromoInAdmin(?,?,?)', [
      pageNumber,
      pagesize,
      search,
    ]);
    response(
      res,
      200,
      'Users List ',
      {
        data: usersList[0],
        totalRecords: usersList[1][0].totalRecords,
        total_pages: usersList[1][0].total_pages,
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.deletePromocode = async (req, res, next) => {
  try {
    const { id } = req.body;

    const data = await executeQuery('CALL DeletePromoCode(?);', [id]);
    if (data[0][0].affected_rows) {
      response(res, 201, 'Deleted successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
