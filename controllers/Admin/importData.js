const { executeQuery, response } = require('../../config/dbConfig');

exports.importData = async (req, res, next) => {
  try {
    const { columns, Data, tableName } = req.body;



    const dataJson = JSON.stringify(Data);

    // const { devicename, latitude, longitude, version, ip } = req.headers;
    await executeQuery('CALL InsertDynamicUnique(?,?,?)', [tableName, dataJson, columns]);

    response(res, 200, 'Data Successfully Executing', {}, true);
  } catch (error) {
    next(error);
  }
};
