const { executeQuery, response } = require('../../config/dbConfig');
const { groupByKey, groupUsersByLocation } = require('../../utils');

exports.usersList = async (req, res, next) => {
  const { pageNumber, pagesize, search, status } = req.body;
  try {
    const usersList = await executeQuery('call GetUsersList(?,?,?,?)', [
      pageNumber,
      pagesize,
      search,
      status,
    ]);
    response(
      res,
      200,
      'Users List ',
      {
        data: usersList[0],
        additionalData: usersList[1][0],
        total_pages: usersList[1][0].totalPages,
        totals: usersList[1][0].totals,
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};
exports.usersAllList = async (req, res, next) => {
  try {
    const usersList = await executeQuery('call GetAllUsersForDropDown()');
    response(
      res,
      200,
      'Users List ',
      {
        data: usersList[0],
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};
exports.usersDetail = async (req, res, next) => {
  const { id } = req.body;
  try {
    const profileDetails = await executeQuery(
      'call GetProfileDetailsbyAdmin(?)',
      [id],
    );

    response(
      res,
      200,
      'Users List ',
      {
        ...profileDetails[0][0],
        // pitch_percentage: globalVar.calculatePercentageChange(
        //   currentWeek[0][0].total_view_count,
        //   prevweekData[0][0].total_view_count,
        // ),
        skills: groupByKey(profileDetails[1], 'master_skill'),
        educations: profileDetails[2],
        experience: profileDetails[3],
        interest: profileDetails[4],
        languages: profileDetails[5],
        links: profileDetails[6],
        profileComplete: profileDetails[7][0].profileComplete,
        media_info: profileDetails[8],
        subscription: profileDetails[9],
        activity: [...profileDetails[10], ...profileDetails[19]],
        reports: profileDetails[11],
        matched: profileDetails[12],
        devices: profileDetails[13],
        unmatched: profileDetails[14],
        blockuser: profileDetails[15],
        likelist: profileDetails[16],
        visit: profileDetails[17],
        rating: profileDetails[18],
        accountLogs: profileDetails[19],
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.getUserHeatMap = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetProfileHeatMap(?)', [
      req.query.id,
    ]);
    response(res, 200, 'Successful', groupUsersByLocation(data[0]), true); //
  } catch (error) {
    next(error);
  }
};

exports.getUserPitchList = async (req, res, next) => {
  const { id } = req.query;
  try {
    const data = await executeQuery('CALL getUserPitchList(?)', [id]);
    response(res, 200, 'Successful', data[0], true); //
  } catch (error) {
    next(error);
  }
};
exports.updateUser = async (req, res, next) => {
  const {
    id,
    about,
    // language_id , interest_id , interest_name ,  language_name ,
    dob,
    designation,
    work_experience,
    country_code,
    phone,
    gender,
  } = req.body;
  const formattedCountryCode = country_code.startsWith('+')
    ? country_code.replace('+', '')
    : `+${country_code}`;

  // Remove the country code from the phone number only if it's at the beginning
  let number = phone.startsWith(formattedCountryCode)
    ? phone.slice(formattedCountryCode.length)
    : phone;

  console.log(about, 'ddfdf');

  //   {
  //     "gender": "Female",
  //     "designation": "18",
  //     "work_experience": 2,
  //     "dob": "1998-03-04",
  //     "phone": "",
  //     "country_code": "",
  //     "languages": []
  // }
  try {
    const datddataa = await executeQuery(
      'CALL updateUserProfileData(?,?,?,?,?,?,?,?)',
      [
        id,
        designation,
        dob,
        work_experience,
        country_code,
        number,
        about,
        gender,
      ],
    );
    response(res, 200, 'Update Successfully', datddataa, true); //
  } catch (error) {
    next(error);
  }
};
function formatPitchData(result) {
  const trendData = result[0]; // User trend data
  const cityData = result[1]; // City-wise view data

  // Convert trend data
  let userTrendData = trendData.map((item, index, arr) => ({
    date: item.view_period, // This is the formatted date from SQL
    users: item.total_views,
    previous: index > 0 ? arr[index - 1].total_views : 0, // Previous day's views
  }));

  // Convert city-wise view data
  let countryData = cityData.map((item) => ({
    city: item.city || 'Unknown', // Handle null cities
    users: item.total_users,
    change: 0, // We'll calculate change below
  }));

  // Calculate percentage change for city data
  for (let i = 1; i < countryData.length; i++) {
    let prev = countryData[i - 1].users || 1; // Avoid division by zero
    countryData[i].change = (
      ((countryData[i].users - prev) / prev) *
      100
    ).toFixed(1);
  }

  return { userTrendData, countryData };
}

exports.getUserPitchStats = async (req, res, next) => {
  const { pitch_id, startDate, endDate, period } = req.body;
  try {
    const pitchDetails = await executeQuery('CALL ViewPitchStats(?,?,?,?)', [
      pitch_id,
      startDate,
      endDate,
      period,
    ]);
    const formattedData = formatPitchData(pitchDetails);
    response(res, 200, 'Successful', formattedData, true); //
  } catch (error) {
    next(error);
  }
};
exports.getAllDesignation = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetDesignations(?)', [search]);
    response(res, 200, 'Experience List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getExperience = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetExperience()', []);
    response(res, 200, 'Experience List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getLanguageList = async (req, res, next) => {
  try {
    const { search = null } = req.query;

    const data = await executeQuery('CALL GetLanguageList(?,?)', [
      search,
      req.userData.id,
    ]);
    response(res, 200, 'Language List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
