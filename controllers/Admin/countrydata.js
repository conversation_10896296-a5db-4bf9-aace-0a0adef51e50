const { executeQuery, response } = require('../../config/dbConfig');

exports.addCountries = async (req, res, next) => {
  try {
    const {
      id = null,
      name,
      iso3,
      short_name,
      phonecode,
      capital,
      currency,
      native,
      region,
      subregion,
    } = req.body;

    const data = await executeQuery(
      'CALL AddUpdateCountry(?,?,?,?,?,?,?,?,?,?);',
      [
        id,
        name,
        iso3,
        short_name,
        phonecode,
        capital,
        currency,
        native,
        region,
        subregion,
      ],
    );

    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0
            ? 'Country updated successfully.'
            : 'Country added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add Institude.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.addState = async (req, res, next) => {
  try {
    const { id = null, country_id, state_name } = req.body;

    const data = await executeQuery('CALL AddUpdateStateByAdmin(?,?,?);', [
      id,
      country_id,
      state_name,
    ]);

    console.log(data[0], 'data[0]', data.affectedRows);
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0 ? 'State updated successfully.' : 'State added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add State.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};
exports.addCity = async (req, res, next) => {
  try {
    const { id = null, country_id, state_id, city_name } = req.body;

    const data = await executeQuery('CALL AddUpdateCityByAdmin(?,?,?,?);', [
      id,
      country_id,
      state_id,
      city_name,
    ]);

    console.log(data[0], 'data[0]', data.affectedRows);
    if (data[0]) {
      const { response_message } = data[0][0];
      response(res, 400, response_message, {}, false); // 409 Conflict
    } else {
      if (data.affectedRows) {
        response(
          res,
          201,
          id > 0 ? 'City updated successfully.' : 'City added successfully.',
          {},
          true,
        ); // 201 Created
      } else {
        response(res, 400, 'Failed to add City.', {}, false); // 409 Conflict
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.GetCountries = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL GetCountries(?,?,?);', [
      pageNumber,
      pagesize,
      search,
    ]);
    console.log(data[1]);
    if (data[0]) {
      response(
        res,
        200,
        'Countries skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Countries.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetStates = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL GetStates(?,?,?);', [
      pageNumber,
      pagesize,
      search,
    ]);
    console.log(data[1]);
    if (data[0]) {
      response(
        res,
        200,
        'Countries skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Countries.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.GetCity = async (req, res, next) => {
  try {
    const { pageNumber, pagesize, search } = req.body;
    const data = await executeQuery('CALL getCities(?,?,?);', [
      pageNumber,
      pagesize,
      search,
    ]);
    if (data[0]) {
      response(
        res,
        200,
        'Countries skill fetched successfully.',
        {
          data: data[0],
          total_pages: data[1][0].total_pages,
          totalRecords: data[1][0].totalRecords,
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Failed to list Countries.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.GetCountryList = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL getAllCountries();', []);
    response(
      res,
      200,
      'Countries skill fetched successfully.',
      { data: data[0] },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.GetStateList = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL getAllstates();', []);
    response(
      res,
      200,
      'Countries skill fetched successfully.',
      { data: data[0] },
      true,
    );
  } catch (error) {
    next(error);
  }
};
exports.GetTotalRecords = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetGeographicalRecords()', []);
    response(
      res,
      200,
      'Institudes List Fetched Successfully',
      {
        Country: data[0][0].Country_Records,
        State: data[0][0].State_Records,
        City: data[0][0].City_Records,
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
