const { executeQuery, response } = require('../../config/dbConfig');
const {
  getTemplates,

  getUserFcm,
} = require('../../utils');
const { notification } = require('../../utils/firebase');

exports.sendNotification = async (req, res, next) => {
  try {
    const { receiver_id, message, topic_name, id } = req.body;
    const userId = req.userData.id;
    var data;
    var isTopic = true;
    const likeTemp = await getTemplates('Admin');
    console.log(receiver_id, message, topic_name, id, 'dddddd');
    // Send notification only if "id" is present

    // Otherwise, insert notification in DB and then send it
    let notification_type = 'admin';

    if (!id)
      data = await executeQuery('CALL AddNotificationbyAdmins(?,?,?,?,?,?)', [
        userId,
        receiver_id,
        'admin_topic',
        message,
        topic_name,
        notification_type,
      ]);

    if (data?.affectedRows || id > 0) {
      if (receiver_id == 'All') {
        await notification(
          topic_name,
          message,
          'all_users',
          {
            message,
            type: likeTemp[0].module,
            created_at: new Date().toISOString(),
          },
          likeTemp[0].module,
          isTopic,
        );
      } else {
        const fcmData = await getUserFcm(receiver_id);
        if (fcmData.length) {
          await notification(
            topic_name,
            message,
            fcmData,
            {
              message,
              type: likeTemp[0].module,
              created_at: new Date().toISOString(),
            },
            likeTemp[0].module,
          );
        }
      }
      return response(res, 201, 'Message sent successfully', {}, true);
    }

    response(res, 409, 'Failed to send notification', {}, false);
  } catch (error) {
    next(error);
  }
};

exports.getNotification = async (req, res, next) => {
  const { pageNumber, pagesize, search } = req.body;
  try {
    const usersList = await executeQuery(
      'call listNotificationInAdmin(?,?,?)',
      [pageNumber, pagesize, search],
    );
    response(
      res,
      200,
      'Users List ',
      {
        data: usersList[0],
        totalRecords: usersList[1][0].total_records,
        total_pages: usersList[1][0].total_pages,
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.deleteNotification = async (req, res, next) => {
  try {
    const { id } = req.body;
    // const data = await executeQuery('call DeleteMessageList(?)',[notification_id]);
    const data = await executeQuery('CALL DeleteMessageList(?);', [id]);
    if (data[0][0].affected_rows) {
      response(res, 201, 'Deleted successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to Delete.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
