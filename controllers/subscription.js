const { executeQuery, response } = require('../config/dbConfig');
const mailer = require('../config/mailerConfig');
const { getUserData, getSubscriptionPlan } = require('../utils');
const { subscriptionMail } = require('../utils/mailhelpers');
const { validateWebhookPayload } = require('../validators/subscription');
const { WebhookHandler, WebhookConfig } = require('../webhook');

exports.addSubscriptionPlan = async (req, res, next) => {
  try {
    const {
      plan_id,
      device_type,
      purchase_token,
      transaction_id,
      currency,
      amount,
    } = req.body;
    const addPlan = await executeQuery(
      'call AddSubscriptionPlan(?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        plan_id,
        device_type,
        purchase_token,
        req.sessionId,
        transaction_id,
        currency,
        amount,
      ],
    );
    const startDate = new Date(addPlan[0][0].start_date);
    const endDate = new Date(addPlan[0][0].expiry_date);
    const options = {
      timeZone: 'UTC',
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    };

    const readableUTCStartDate = startDate.toLocaleString('en-US', options);
    const readableUTCEndDate = endDate.toLocaleString('en-US', options);

    console.log('Readable UTC Start Date:', readableUTCStartDate);
    console.log('Readable UTC End Date:', readableUTCEndDate);

    const userData = await getUserData(req.userData.id);
    const planName = await getSubscriptionPlan(plan_id);

    const { email: currentEmail, name } = userData[0];
    mailer.sendMail(
      subscriptionMail(
        name,
        currentEmail,
        planName,
        readableUTCStartDate,
        readableUTCEndDate,
      ),
      async (err, info) => {
        if (err) {
          console.error('Error sending mail:', err);
        }
        console.log(info, 'mail status');
      },
    );
    response(
      res,
      200,
      'Added Subscription  Successfully.',
      addPlan[0][0],
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.getSubscriptionPlans = async (req, res, next) => {
  try {
    const subscriptionData = await executeQuery('call GetSubscriptions()');
    response(
      res,
      200,
      'Subscription Details Fetched  Successfully.',
      subscriptionData[0],
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.validatePromoCode = async (req, res, next) => {
  try {
    const { promo_code } = req.body;
    const promoCode = await executeQuery('call ValidatePromoCode(?, ?)', [
      req.userData.id,
      promo_code,
    ]);
    response(
      res,
      200,
      'Subscription Details Fetched  Successfully.',
      promoCode[0][0],
      true,
    );
  } catch (error) {
    next(error);
  }
};

// Platform-specific handlers
async function handleAndroidSubscriptionCanceled(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'android',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send cancellation notification
        console.log('Sending cancellation notification:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'SUBSCRIPTION_CANCELED',
    source: 'android',
    data: {
      purchaseToken: data.purchaseToken,
      expiryDate: data.expiryDate,
    },
  });
}

async function handleIosInitialBuy(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'ios',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send purchase confirmation
        console.log('Sending purchase confirmation:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'INITIAL_BUY',
    source: 'ios',
    data: {
      transactionId: data.transactionId,
      expiryDate: data.expiryDate,
    },
  });
}

async function handleAndroidSubscriptionPurchased(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'android',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send purchase confirmation
        console.log('Sending purchase confirmation:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'SUBSCRIPTION_PURCHASED',
    source: 'android',
    data: {
      purchaseToken: data.purchaseToken,
      expiryDate: data.expiryDate,
    },
  });
}

async function handleIosCancel(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'ios',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send cancellation notification
        console.log('Sending cancellation notification:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'CANCEL',
    source: 'ios',
    data: {
      transactionId: data.transactionId,
      expiryDate: data.expiryDate,
    },
  });
}

async function handleAndroidSubscriptionRenewed(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'android',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send renewal confirmation
        console.log('Sending renewal confirmation:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'SUBSCRIPTION_RENEWED',
    source: 'android',
    data: {
      purchaseToken: data.purchaseToken,
      expiryDate: data.expiryDate,
    },
  });
}

async function handleIosRenewal(data) {
  const dbConfig = {
    executeQuery: async (query, values) => {
      return await executeQuery(query, values);
    },
  };

  const config = new WebhookConfig({
    dbConfig,
    webhookType: 'subscription',
    platform: 'ios',
    notificationConfig: {
      enabled: true,
      handler: async (notificationData) => {
        const { event, statusUpdate } = notificationData;
        // Send renewal confirmation
        console.log('Sending renewal confirmation:', event, statusUpdate);
      },
    },
  });

  const handler = new WebhookHandler(config);
  return await handler.handleEvent({
    type: 'RENEWAL',
    source: 'ios',
    data: {
      transactionId: data.transactionId,
      expiryDate: data.expiryDate,
    },
  });
}

async function updateSubscriptionStatus(subscriptionData) {
  try {
    const {
      platform,
      event,
      data: {
        subscriptionId,
        purchaseToken,
        transactionId,
        expiryDate,
        // status, // might be deprecated if we're now using DB mapping
      },
    } = subscriptionData;

    const val = [platform];

    const eventSql = `
      SELECT * FROM subscription_event_types WHERE platform = ?;
    `;
    const eventTypes = await executeQuery(eventSql, val);

    const planMap = {
      'technbuddy.app.premium.yearly': 'Premium Yearly',
      'technbuddy.app.premium.monthly': 'Premium Monthly',
      'com.techNbuddy.yearly': 'Premium Yearly',
      'com.techNbuddy.monthly': 'Premium Monthly',
    };

    const planName = planMap[subscriptionId];

    console.log(event, 'event in bodddd');
    // Find the matching event config
    const matchedEvent = eventTypes.find((e) => e.event_type === event);

    if (!matchedEvent) {
      console.warn(`Unhandled event type: ${event} for platform: ${platform}`);
      return;
    }

    const newStatusId = matchedEvent.id;

    console.log('newwwwwwwwwwwwwwww', expiryDate);
    const resultingStatus = matchedEvent.resulting_status || 'expired'; // Fallback if needed

    // For expired/cancelled subscriptions, set expiry_date to current date if not provided
    let finalExpiryDate = expiryDate;
    if (
      (resultingStatus == 'expired' || resultingStatus == 'cancelled') &&
      !expiryDate
    ) {
      // finalExpiryDate=subscriptionPlanRows[0].trial_days
      finalExpiryDate = new Date().toISOString();
    }

    // Convert to MySQL datetime format
    const formatDateForMySQL = (dateString) => {
      if (!dateString) return null;
      const date = new Date(dateString);
      return date.toISOString().slice(0, 19).replace('T', ' ');
    };
    const sql =
      'CALL UpdateUserSubscriptionStatusWithWebHook(?, ?, ?, ?, ?, ?,?  )';
    const values = [
      event,
      newStatusId,
      formatDateForMySQL(expiryDate),
      purchaseToken || null,
      transactionId || null,
      platform,
      planName,
    ];

    const result = await executeQuery(sql, values);

    if (result.affectedRows === 0) {
      console.warn('No subscription found to update:', {
        platform,
        subscriptionId,
        purchaseToken,
        transactionId,
      });
    } else {
      console.log('Successfully updated subscription:', {
        platform,
        subscriptionId,
        eventType: event,
        newStatusId,
        expiryDate: formatDateForMySQL(finalExpiryDate),
      });
    }

    return result;
  } catch (error) {
    console.error('Error updating subscription status:', error);
    throw error;
  }
}

exports.subscriptionWebhook = async (req, res, next) => {
  try {
    const { error, value } = validateWebhookPayload(req.body);

    if (error) {
      console.error('Webhook validation failed:', error);
      return response(res, 400, 'Invalid webhook payload', null, false);
    }

    // Update subscription status
    await updateSubscriptionStatus(value);

    // Handle platform-specific notifications
    if (value.platform === 'android') {
      switch (value.event) {
      case 'SUBSCRIPTION_CANCELED':
        await handleAndroidSubscriptionCanceled(value.data);
        break;
      case 'SUBSCRIPTION_PURCHASED':
        await handleAndroidSubscriptionPurchased(value.data);
        break;
      case 'SUBSCRIPTION_RENEWED':
        await handleAndroidSubscriptionRenewed(value.data);
        break;
      }
    } else if (value.platform === 'ios') {
      switch (value.event) {
      case 'SUBSCRIPTION_PURCHASED':
        await handleIosInitialBuy(value.data);
        break;
      case 'SUBSCRIPTION_RENEWED':
        await handleIosRenewal(value.data);
        break;
      case 'SUBSCRIPTION_CANCELED':
        await handleIosCancel(value.data);
        break;
      }
    }

    response(res, 200, 'Webhook processed successfully', {}, true);
  } catch (error) {
    console.error('Webhook processing error:', error);
    next(error);
  }
};
