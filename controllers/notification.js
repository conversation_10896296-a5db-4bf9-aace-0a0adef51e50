const { executeQuery, response } = require('../config/dbConfig');
const {
  insertAction,
  getUserData,
  getTemplates,

  getUserFcm,
  getUserMatchId,
} = require('../utils');
const { notification } = require('../utils/firebase');

exports.notificationlist = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { page_number, page_size } = req.body;
    const data = await executeQuery('CALL getNotificationList(?,?,?)', [
      userId,
      page_number,
      page_size,
    ]);

    response(
      res,
      200,
      'Notification List Fetched Successfully',
      // data[0],
      {
        notification_list: data[1],
        total_pages: data[2][0]?.total_pages || 0,
        unread_count: data[0][0]?.read_status || 0,
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};
exports.notificationRead = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { notification_id = 0 } = req.body;
    const activity_id = await insertAction(req, 'Notification read');
    const data = await executeQuery('CALL readNotification(?,?,?)', [
      userId,
      notification_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Notification Read Successfully', {}, true); // 201 Created
    } else {
      response(res, 409, 'Failed to Read  notification', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.notificationDelete = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { notification_ids } = req.body;
    const activity_id = await insertAction(req, 'Notification delete');

    const data = await executeQuery('CALL deleteNotification(?,?,?)', [
      userId,
      notification_ids,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Notification Delete Successfully', {}, true); // 201 Created
    } else {
      response(res, 409, 'Failed to Delete notification', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
exports.sendChatNotification = async ({
  senderId,
  receiverId,
  message,
  type,
}) => {
  const userData = await getUserData(senderId);
  const likeTemp = await getTemplates(type);
  const fcmData = await getUserFcm(receiverId);
  const getMatchData = await getUserMatchId(senderId, receiverId);

  let date = new Date();
  let now_utc = Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds(),
  );

  if (fcmData.length) {
    await notification(
      userData[0].name,
      message,
      fcmData,
      {
        match_id: getMatchData.toString(),
        sender_id: senderId.toString(),
        sender_name: userData[0].name,
        message: message,
        type: likeTemp[0].module,
        sender_profile_pic: userData[0].profile_pic,
        created_at: new Date(now_utc).toISOString(),
      },
      likeTemp[0].module,
    );
  }
};

exports.chat = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { other_user_id, message, type } = req.body;

    await this.sendChatNotification({
      senderId: userId,
      receiverId: other_user_id,
      message,
      type,
    });

    response(res, 201, 'Chat notification sent successfully', {}, true);
  } catch (error) {
    next(error);
  }
};

exports.notificationUnreadReadCount = async (req, res, next) => {
  try {
    const userId = req.userData.id;

    const data = await executeQuery('CALL GetUnreadNotifications(?)', [userId]);
    if (data.length) {
      response(res, 201, 'Fetch Unread Count Successfully', data[0][0], true); // 201 Created
    } else {
      response(res, 409, 'Failed to Read  notification', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};
