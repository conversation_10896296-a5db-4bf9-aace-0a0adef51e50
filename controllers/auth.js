const { awsFunctions } = require('../config/awsConfig');
const { executeQuery, response } = require('../config/dbConfig');
const { admin } = require('../config/firebaseConfig');
const mailer = require('../config/mailerConfig');
const { sendOTP, verifyOTP } = require('../config/twilioConfig');
const { insertAction, getUserData, getUserFcm } = require('../utils');
const {
  notification,
  subscribeUserToTopic,
  unSubscribeUserToTopic,
} = require('../utils/firebase');
const { generateToken } = require('../utils/jwtUtils');
const { mailOptionsGreet, mailRegister } = require('../utils/mailhelpers');

exports.sendOtp = async (req, res, next) => {
  try {
    const { phone_num, country_code } = req.body;
    const userId = req.userData.id;
    const phone = `${country_code}${phone_num}`.replace(/\s+/g, '');
    const result = await sendOTP(userId, phone, country_code);
    console.log(result, 'result');
    if (result?.status) {
      response(
        res,
        200,
        result?.status,
        {},
        result.p_status_bool == 1 ? true : false,
      ); // 200 OK
    } else {
      response(res, 400, 'Failed to send OTP.', {}, false);
    }
  } catch (error) {
    next(error);
  }
};

exports.verifyOtp = async (req, res, next) => {
  try {
    const { otp, phone_num, country_code } = req.body;
    const phone = `${country_code}${phone_num}`.replace(/\s+/g, '');
    const verifyStatus = await verifyOTP(phone, otp);
    if (verifyStatus.status === 'approved') {
      const data = await executeQuery('CALL VerifyOtp(?)', [req.userData.id]);
      if (data[0].length)
        response(
          res,
          200,
          data[0][0].message,
          {},
          Boolean(data[0][0].statusType),
        );
    } else {
      response(res, 400, 'OTP verification failed.', {}, false);
    }
    // const data = await executeQuery('CALL VerifyOtp(?,?)', [
    //   req.userData.id,
    //   otp,
    // ]);
    // if (data[0].length) {
    //   response(
    //     res,
    //     200,
    //     data[0][0].message,
    //     {},
    //     Boolean(data[0][0].statusType),
    //   ); // 200 OK
    // } else {
    //   response(res, 400, 'OTP verification failed.', {}, false); // 400 Bad Request
    // }
  } catch (error) {
    next(error);
  }
};

exports.register = async (req, res, next) => {
  try {
    const {
      name,
      social_id,
      social_type,
      social_profile_pic,
      email,
      device_token,
      voip_token,
      fcm_token,
    } = req.body;
    const { devicename, latitude, longitude, version, ip } = req.headers;
    const data = await executeQuery(
      'CALL RegisterProfile(?,?,?,?,?,?,?, ?,?)',
      [
        name,
        social_id,
        social_type,
        devicename,
        latitude,
        longitude,
        social_profile_pic,
        email,
        ip,
      ],
    );
    console.log(data, 'data from registerProfile /LOGIN');
    if (data[0].length) {
      const { status_id, id } = data[0][0];
      const auth_token = await generateToken(id);
      if (status_id == 1) {
        await executeQuery('CALL HandleUserSession(?,?,?,?,?,?,?,?,?,?);', [
          id,
          device_token,
          devicename,
          fcm_token,
          version,
          ip,
          latitude,
          longitude,
          auth_token,
          voip_token || '',
        ]);
        await subscribeUserToTopic(fcm_token);
      }

      if (data[0][0].is_prev_deleted) {
        await admin.auth().deleteUser(data[0][0].p_uid);
      }

      response(
        res,
        201,
        data[0][0].isReturned_User
          ? 'Account successfully recovered.'
          : 'Registration successful.',
        {
          ...data[0][0],
          auth_token,
          profile_steps: data[1],
        },
        true,
      ); // 201 Created
    } else {
      response(res, 400, 'Registration failed.', {}, false); // 400 Bad Request
    }
  } catch (error) {
    next(error);
  }
};

exports.profileSetup = async (req, res, next) => {
  try {
    const {
      full_name,
      email,
      phone,
      designation_id,
      work_experience_id,
      dob,
      gender,
      about,
      country_code,
      promo_code,
    } = req.body;
    const activity_id = await insertAction(req, 'Update profile');
    const userData = await getUserData(req.userData.id);
    const { total_profile_step, email: currentEmail, name } = userData[0];
    console.log(total_profile_step, 'total_profile_step');
    if (total_profile_step < 2) {
      mailer.sendMail(
        mailOptionsGreet(name, currentEmail),
        async (err, info) => {
          if (err) {
            console.error('Error sending mail:', err);
          }
          console.log(info, 'mail status');
        },
      );
    }
    const data = await executeQuery(
      'CALL UpdateProfile(?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        full_name,
        email,
        phone,
        designation_id,
        work_experience_id,
        dob,
        gender,
        about || '',
        country_code,
        activity_id,
        promo_code,
      ],
    );
    if (promo_code) {
      const isApplied = await executeQuery('CALL ValidatePromoCode(?,?)', [
        req.userData.id,
        promo_code,
      ]);
      if (isApplied[0][0].p_is_valid) {
        await executeQuery('CALL apply_promo_code(?,?)', [
          req.userData.id,
          promo_code,
        ]);
      }
    }

    if (data[0].length) {
      response(res, 200, 'Profile updated successfully.', data[0][0], true); // 200 OK
    } else {
      response(res, 400, 'Profile update failed.', {}, false); // 400 Bad Request
    }
  } catch (error) {
    next(error);
  }
};

exports.addSubscription = async (req, res, next) => {
  try {
    const { promo_code } = req.body;
    const isApplied = await executeQuery('CALL ValidatePromoCode(?,?)', [
      req.userData.id,
      promo_code,
    ]);
    if (isApplied[0][0].CREATE DEFINER=`tnb_production_usr`@`%` PROCEDURE `ValidatePromoCode`(
    IN p_user_id INT,
    IN p_promo_code VARCHAR(50)
)
BEGIN
    DECLARE v_promo_id INT DEFAULT NULL;
    DECLARE v_usage_type VARCHAR(10); -- ENUM replaced with VARCHAR
    DECLARE v_max_usage INT DEFAULT 0;
    DECLARE v_max_usage_per_user INT DEFAULT 0;
    DECLARE v_used_count INT DEFAULT 0;
    DECLARE v_valid_from DATE;
    DECLARE v_valid_to DATE;
    DECLARE v_status_id INT DEFAULT 0;
    DECLARE v_user_usage INT DEFAULT 0;
    DECLARE v_subscription_id INT;
    DECLARE v_subscription_name VARCHAR(255);
    DECLARE p_status_message VARCHAR(225);
    DECLARE p_is_valid BOOLEAN;
    DECLARE p_promo_validity_days VARCHAR(25);
    DECLARE v_months INT DEFAULT 0;
	DECLARE v_weeks INT DEFAULT 0;
    DECLARE v_days INT DEFAULT 0;
    DECLARE v_validity_text VARCHAR(100);

    

    -- Plan details
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_currency VARCHAR(10);
    DECLARE v_validity_days INT;

    -- Initialize output values
    SET p_status_message = 'Invalid promo code';
    SET p_is_valid = FALSE;

    -- Fetch promo code details
    SELECT id, usage_type, max_usage, max_usage_per_user, used_count, valid_from, valid_to, status_id, subscription_id,validity_days
    INTO v_promo_id, v_usage_type, v_max_usage, v_max_usage_per_user, v_used_count, v_valid_from, v_valid_to, v_status_id, v_subscription_id,p_promo_validity_days
    FROM promocodes
    WHERE LOWER(TRIM(code)) = LOWER(TRIM(p_promo_code))
      AND status_id = 1
      AND CURDATE() BETWEEN valid_from AND valid_to
    LIMIT 1;

    -- If promo code does not exist or is inactive
    IF v_promo_id IS NULL THEN
        SET p_status_message = 'Invalid or expired promo code';
    ELSE
        -- Check global usage limit
        IF v_usage_type = 'single' AND v_used_count >= v_max_usage THEN
            SET p_status_message = 'Promo code usage limit reached';
        ELSE
            -- Check user's specific usage count
            SELECT usage_count INTO v_user_usage
            FROM user_promocode_usage
            WHERE user_id = p_user_id AND promocode_id = v_promo_id
            LIMIT 1;

            IF v_user_usage IS NULL THEN
                SET v_user_usage = 0;
            END IF;

            IF v_user_usage >= v_max_usage_per_user THEN
                SET p_status_message = 'User has exceeded promo usage limit';
            ELSE
                SET p_status_message = 'Promo code is valid';
                SET p_is_valid = TRUE;
            END IF;
        END IF;
    END IF;

    -- Fetch subscription plan details if promo was found
    IF v_subscription_id IS NOT NULL THEN
        SELECT plan_name, price, currency, validity_days
        INTO v_subscription_name, v_price, v_currency, v_validity_days
        FROM subscription_plans
        WHERE id = v_subscription_id
        LIMIT 1;
    END IF;
    
    -- Break down days into months, weeks, days
SET v_months = FLOOR(p_promo_validity_days / 30);
SET v_weeks = FLOOR((p_promo_validity_days % 30) / 7);
SET v_days = p_promo_validity_days % 7;

-- Build human-readable string
SET v_validity_text = '';
IF v_months > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_months, ' month', IF(v_months > 1, 's', ''), ' ');
END IF;
IF v_weeks > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_weeks, ' week', IF(v_weeks > 1, 's', ''), ' ');
END IF;
IF v_days > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_days, ' day', IF(v_days > 1, 's', ''), '');
END IF;

-- Trim any trailing space
SET v_validity_text = TRIM(v_validity_text);

    
    SET v_validity_text = '';
IF v_months > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_months, ' month', IF(v_months > 1, 's', ''), ' ');
END IF;
IF v_weeks > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_weeks, ' week', IF(v_weeks > 1, 's', ''), ' ');
END IF;
IF v_days > 0 THEN
    SET v_validity_text = CONCAT(v_validity_text, v_days, ' day', IF(v_days > 1, 's', ''), '');
END IF;

-- Trim any trailing space
SET v_validity_text = TRIM(v_validity_text);

    -- Final response
    SELECT 
        p_is_valid,
        p_status_message AS message,
        v_subscription_id AS plan_id,
        v_subscription_name AS plan_name,
        v_validity_days AS plan_validity,
        v_price AS original_price,
        '0.0' AS discounted_price,
        v_currency AS currency,
        p_promo_validity_days AS promo_offered_validity,
        v_validity_text AS promo_offered_validity_readable,
        DATEDIFF(v_valid_to, CURDATE()) AS promo_remaining_days;
END) {
      const result = await executeQuery('CALL apply_promo_code(?,?)', [
        req.userData.id,
        promo_code,
      ]);
      response(
        res,
        200,
        'Promo Code Applied successfully.',
        result[0][0],
        true,
      ); // 200 OK
    } else {
      response(res, 400, 'Invalid Promo Code.', {}, false); // 400 Bad Request
    }
  } catch (error) {
    next(error);
  }
};

exports.addSkills = async (req, res, next) => {
  try {
    const { skills } = req.body;
    const activity_id = await insertAction(req, 'Add Skill');
    const data = await executeQuery('CALL AddUserSkills(?,?,?);', [
      req.userData.id,
      skills,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Skills added successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to add skills.', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.updateProfilePic = async (req, res, next) => {
  try {
    const { profile_pic, media_source, media_score } = req.body;
    const activity_id = await insertAction(req, 'Update profile pic');
    const data = await executeQuery(
      'CALL AddUserMedia(?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        profile_pic,
        'Profile_Pic',
        5,
        media_source,
        '',
        0,
        media_score,
        activity_id,
        '',
        '',
      ],
    );
    if (data[0].length) {
      response(res, 200, 'Profile picture updated successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Failed to update profile picture.', {}, false); // 400 Bad Request
    }
  } catch (error) {
    next(error);
  }
};

exports.updateProfilePitch = async (req, res, next) => {
  try {
    const {
      media_path,
      media_source,
      pitch_script,
      light_space,
      dress,
      length,
      size,
    } = req.body;
    const jobIds = await awsFunctions.startVideoModeration(media_path);
    const { moderationJobId, faceDetectionJobId, originalUrl, processedUrl } =
      jobIds;
    console.log(
      `Started moderation job: ${moderationJobId} , face detection job: ${faceDetectionJobId}`,
    );

    // Use processed URL if available, otherwise use original
    const finalMediaPath = processedUrl || originalUrl || media_path;
    const activity_id = await insertAction(req, 'Update profile pitch');
    const data = await executeQuery(
      'CALL AddUserMedia(?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        finalMediaPath, // Use processed URL if available
        'Profile_Pitch',
        6,
        media_source,
        length,
        size,
        0,
        activity_id,
        moderationJobId,
        faceDetectionJobId,
      ],
    );
    const pitchConfig = await executeQuery(
      'CALL InsertUserPitchConfig(?,?,?,?)',
      [req.userData.id, pitch_script, light_space, dress],
    );
    let notification_type = 'system_genrated';

    await executeQuery('CALL AddNotificationbyAdmins(?,?,?,?,?,?)', [
      data[0][0].new_pic_id,
      req.userData.id,
      'pitch',
      'Your Profile Pitch  is  been  Review',
      'system_genrated',
      notification_type,
    ]);

    if (data[0].length && pitchConfig[0].length) {
      const fcmData = await getUserFcm(req.userData.id);
      if (fcmData.length) {
        await notification(
          'Profile Pitch Review In Progress',
          'Your Profile Pitch  is  been  Review',
          fcmData,
          {
            pitch_id: data[0][0].new_pic_id.toString(),
            status: 'Rejected',
          },
          'Profile_Detail',
        );
      }
      response(
        res,
        200,
        'Profile pitch uploaded and queued for review. You will be notified once processing is complete.',
        {
          moderationJobId,
          faceDetectionJobId,
          status: 'processing',
          originalUrl: originalUrl || media_path,
          processedUrl: processedUrl,
          videoProcessed: !!processedUrl,
        },
        true,
      ); // 200 OK
    } else response(res, 400, 'Failed to update profile pitch.', {}, false); // 400 Bad Request
  } catch (error) {
    next(error);
  }
};
exports.addGetScript = async (req, res, next) => {
  try {
    const user_id = req.userData.id;
    const { script, id = null, prompt } = req.body;

    const data = await executeQuery('CALL AddGetScript(?,?,?,?)', [
      user_id,
      script,
      id,
      prompt,
    ]);
    response(
      res,
      200,
      'Script fetched successfully.',
      { limitStats: data[0][0], list: data[1] },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getSkills = async (req, res, next) => {
  try {
    const { skill_id = null, search = null } = req.query;
    const data = await executeQuery('CALL GetSkills(?,?)', [skill_id, search]);
    response(res, 200, 'Skills fetched successfully.', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.lookingUpFor = async (req, res, next) => {
  try {
    const {
      profile_pitch_video,
      lookingfor,
      device_token,
      fcm_token,
      voip_token,
      designation_id,
      work_exp_id,
      purpose,
    } = req.body;
    const { devicename, latitude, longitude, version, ip } = req.headers;
    const activity_id = await insertAction(req, 'Looking up for');
    const status = await executeQuery('CALL LookingUpFor(?,?,?,?,?)', [
      req.userData.id,
      profile_pitch_video,
      lookingfor,
      activity_id,
      purpose,
    ]);
    await executeQuery('call updateLookingForExpDesignation(?,?,?)', [
      req.userData.id,
      designation_id,
      work_exp_id,
    ]);
    if (status.affectedRows > 0) {
      await executeQuery('CALL HandleUserSession(?,?,?,?,?,?,?,?,?,?);', [
        req.userData.id,
        device_token,
        devicename,
        fcm_token,
        version,
        ip,
        latitude,
        longitude,
        req.token,
        voip_token || '',
      ]);
      await subscribeUserToTopic(fcm_token);
      const userData = await getUserData(req.userData.id);
      mailer.sendMail(
        mailRegister(userData[0].name, userData[0].email),
        async (err, info) => {
          if (err) {
            console.error('Error sending mail:', err);
          }
          console.log(info, 'mail status');
        },
      );

      response(res, 200, 'Looking up for updated successfully.', {}, true); // 200
    } else response(res, 400, 'Failed to update looking up for.', {}, false); //
  } catch (error) {
    console.log(error, 'error');
    next(error);
  }
};

exports.addAbout = async (req, res, next) => {
  try {
    const { about } = req.body;

    const status = await executeQuery('CALL AddAbout(?,?)', [
      req.userData.id,
      about,
    ]);
    if (status.affectedRows > 0)
      response(res, 200, 'Updated successfully.', {}, true); // 200 OK
    else response(res, 400, 'Update Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addOtherSkill = async (req, res, next) => {
  try {
    const { master_skill_id, skill_name, skill_for } = req.body;
    const status = await executeQuery('CALL AddOtherSkill(?,?,?,?)', [
      req.userData.id,
      master_skill_id,
      skill_name,
      skill_for,
    ]);

    if (status[0].length > 0) {
      if (status[0][0].result) {
        response(res, 409, 'Skill is already exist.', {}, false); // 200 OK
      } else
        response(res, 200, 'Skill Added  Successfully.', status[0][0], true); // 200 OK
    } else response(res, 400, 'Skill Add  Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.removeOtherSkill = async (req, res, next) => {
  try {
    const { skill_id } = req.body;
    const status = await executeQuery('CALL RemoveOtherSkill(?,?)', [
      req.userData.id,
      skill_id,
    ]);
    if (status.affectedRows > 0)
      response(res, 200, 'Skill Removed  Successfully.', {}, true); // 200 OK
    else response(res, 400, 'Skill Remove  Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.logout = async (req, res, next) => {
  try {
    const fcmData = await getUserFcm(req.userData.id);
    console.log(fcmData, 'fcmData');
    await unSubscribeUserToTopic(fcmData);
    const status = await executeQuery('CALL Logout(?,?)', [
      req.token,
      req.userData.id,
    ]);

    if (status.affectedRows > 0) {
      response(res, 200, 'Logged out successfully.', {}, true);
    } // 200 OK
    else response(res, 400, 'Logout Failed.', {}, false); // 400 OK
  } catch (error) {
    next(error);
  }
};

exports.deleteAccount = async (req, res, next) => {
  try {
    const { UID } = req.body;
    const { devicename, ip } = req.headers;

    const status = await executeQuery('CALL DeleteAccount(?,?,?,?)', [
      req.userData.id,
      devicename,
      ip,
      UID,
    ]);
    if (status.affectedRows > 0) {
      response(res, 200, 'Account Deleted Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Account Delete Failed.', {}, false); // 200 OK
    }
  } catch (error) {
    next(error);
  }
};
