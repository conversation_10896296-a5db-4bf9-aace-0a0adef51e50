const { WebhookConfig } = require('../../webhook');
const { executeQuery } = require('../../config/dbConfig');
const { validateWebhookPayload } = require('../../validators/subscription');
const { logger } = require('../../utils/logger');
const SubscriptionWebhookHandler = require('../../webhook/handlers/subscription');

class SubscriptionWebhookController {
  constructor() {
    this.dbConfig = {
      executeQuery: async (query, values) => {
        return await executeQuery(query, values);
      },
    };
  }

  // Main webhook handler
  async handleWebhook(req, res) {
    try {
      logger.info('Received webhook request');

      // Validate the webhook payload
      const { error, value } = validateWebhookPayload(req.body);
      if (error) {
        logger.error('Webhook validation failed:', error);
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      const { platform, event, data } = value;
      logger.info(`Processing ${platform} webhook event: ${event}`);

      // Create webhook configuration
      const config = new WebhookConfig({
        dbConfig: this.dbConfig,
        webhookType: 'subscription',
        platform: platform,
        notificationConfig: {
          enabled: true,
          handler: async (notificationData) => {
            logger.info('Processing notification:', notificationData);
          },
        },
      });

      // Create subscription webhook handler
      const handler = new SubscriptionWebhookHandler(config);

      // Handle the webhook event
      const result = await handler.handleWebhook({
        platform,
        event,
        data: {
          ...data,
          platform,
        },
      });

      // Return success response
      return res.status(200).json({
        success: true,
        message: `Webhook processed successfully for ${platform}`,
        data: result,
      });
    } catch (error) {
      logger.error('Error processing webhook:', error);
      return res.status(500).json({
        success: false,
        message: 'Error processing webhook',
        error: error.message,
      });
    }
  }
}

module.exports = new SubscriptionWebhookController();
