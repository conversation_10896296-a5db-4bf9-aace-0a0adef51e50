const { executeQuery, response } = require('../config/dbConfig');
const {
  groupByKey,
  insertAction,
  globalVar,
  formatFeatures,
} = require('../utils');

exports.getProfileDetails = async (req, res, next) => {
  try {
    console.log(req.userData.id);
    var date = new Date();

    console.log(date.getDay(), ' date.getDay()');
    // let CurrentWeekStart = new Date(date.setDate(date.getDate()));

    let WeekStart = date.getDate() - date.getDay() - 13;
    let WeekFrom = new Date(date.setDate(WeekStart));
    let WeekEnd = date.getDate() - date.getDay() + 14;

    let CurrentWeekStart = date.getDate() - date.getDay() + 7;
    let CurrentWeekStartWeekTo = new Date(date.setDate(CurrentWeekStart));
    let WeekTo = new Date(date.setDate(WeekEnd));
    let formattedlastWeekStart = WeekFrom.toISOString().split('T')[0];
    let formattedlastWeekEnd = WeekTo.toISOString().split('T')[0];
    let formattedCurrentWeek =
      CurrentWeekStartWeekTo.toISOString().split('T')[0];
    // let CurrentWeekEnd = date.getDate() - date.getDay() + 7;

    const profileDetails = await executeQuery('CALL GetProfileDetails(?)', [
      req.userData.id,
    ]);
    const prevweekData = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
      profileDetails[0][0]?.pitch_id,
      formattedlastWeekStart,
      formattedCurrentWeek,
    ]);
    const currentWeek = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
      profileDetails[0][0]?.pitch_id,
      formattedCurrentWeek,
      formattedlastWeekEnd,
    ]);

    if (profileDetails[0].length > 0)
      response(
        res,
        200,
        'Profile Details Fetched  Successfully.',
        {
          ...profileDetails[0][0],
          pitch_percentage: globalVar.calculatePercentageChange(
            currentWeek[0][0].total_view_count,
            prevweekData[0][0].total_view_count,
          ),
          skills: groupByKey(profileDetails[1], 'master_skill'),
          educations: profileDetails[2],
          experience: profileDetails[3],
          interest: profileDetails[4],
          languages: profileDetails[5],
          links: profileDetails[6],
          profileComplete: profileDetails[7][0].profileComplete,
        },
        true,
      );
    else response(res, 400, 'Profile Details Fetched  Failed.', {}, false);
  } catch (error) {
    next(error);
  }
};
// exports.getProfileDetails = async (req, res, next) => {
//   try {

//     function getRandomLatitude() {
//       return (Math.random() * (90 - (-90)) + (-90)).toFixed(6); // Random latitude between -90 and 90
//     }

//     function getRandomLongitude() {
//       return (Math.random() * (180 - (-180)) + (-180)).toFixed(6); // Random longitude between -180 and 180
//     }

//     function generateUser(index) {
//       return {
//         id: index + 1,
//         name: `User ${index + 1}`,
//         work_experience: index % 2 === 0 ? '0 - 6 Months' : '6 - 12 Months',
//         designation: index % 2 === 0 ? 'Designer' : 'Developer',
//         dob: new Date(1980, 7, 15).toISOString(),
//         company: `Company ${Math.ceil(Math.random() * 10)}`,
//         gender: index % 2 === 0 ? 'Male' : 'Female',
//         profile_pic: `https://example.com/profile/${index + 1}.jpg`,
//         profile_pitch: `https://example.com/pitch/${index + 1}.mp4`,
//         is_profile_pitch: 1,
//         media_score: Math.floor(Math.random() * 101),
//         verify_profile_status: 1,
//         email: `user${index + 1}@example.com`,
//         phone: `******-01${Math.floor(Math.random() * 10000)}`,
//         geo: {
//           latitude: parseFloat(getRandomLatitude()),
//           longitude: parseFloat(getRandomLongitude())
//         },
//         address: `Address ${index + 1}, City ${Math.ceil(Math.random() * 10)}, State ${Math.ceil(Math.random() * 5)}, ZIP 160${Math.floor(Math.random() * 1000)}`,
//         skills: [
//           {
//             key: 'Web Development',
//             values: [
//               {
//                 name: 'JavaScript',
//                 added_by_user: index % 2
//               },
//               {
//                 name: 'CSS',
//                 added_by_user: (index + 1) % 2
//               }
//             ]
//           }
//         ],
//         educations: [
//           {
//             institution: `University ${Math.ceil(Math.random() * 5)}`,
//             description: '',
//             is_current: 1
//           },
//           {
//             institution: `University ${Math.ceil(Math.random() * 5)}`,
//             description: '',
//             is_current: 1
//           }
//         ],
//         experience: [
//           {
//             company_name: `Company ${Math.ceil(Math.random() * 10)}`,
//             position_name: index % 2 === 0 ? 'Software Engineer' : 'Product Manager',
//             employment_type: 'Full-Time',
//             location: `Location ${Math.ceil(Math.random() * 10)}`
//           }
//         ],
//         interest: [
//           {
//             name: 'Gardening',
//             added_by_user: index % 2
//           },
//           {
//             name: 'Reading',
//             added_by_user: (index + 1) % 2
//           }
//         ],
//         languages: [
//           {
//             name: 'English'
//           },
//           {
//             name: 'Hindi'
//           }
//         ],
//         links: [
//           {
//             link: `https://linkedin.com/user${index}`,
//             title: 'LinkedIn'
//           },
//           {
//             link: `https://github.com/user${index}`,
//             title: 'GitHub'
//           }
//         ]
//       };
//     }

//     const users = Array.from({ length: 500 }, (_, index) => generateUser(index));

//     response(
//       res,
//       200,
//       'Profile Details Fetched  Successfully.',
//       users,
//       true,
//     );
//   } catch (error) {
//     next(error);
//   }
// };

exports.getProfileSettings = async (req, res, next) => {
  try {
    const profileSettings = await executeQuery(
      'CALL manage_user_settings(?,?)',
      [req.userData.id, req.sessionId],
    );
    if (profileSettings[0].length > 0)
      response(
        res,
        200,
        'Profile Settings Fetched  Successfully.',
        {
          ...profileSettings[0][0],
          location: profileSettings[2].length ? profileSettings[2][0] : {},
          looking_for_skills: groupByKey(profileSettings[1], 'master_skill'),
          subscription: profileSettings[3].length ? profileSettings[3][0] : {},
          features: profileSettings[4].length
            ? formatFeatures(profileSettings[4])
            : {},
        },
        true,
      );
    // 200 OK
    else response(res, 400, 'Profile Settings Fetched  Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.updateProfileSettings = async (req, res, next) => {
  try {
    const {
      id,
      swapping_in_location,
      profile_has_pitch_video,
      max_distance,
      age_range_from,
      age_range_to,
      show_me_on_techbuddy,
      dont_show_age,
      make_distance_invisible,
      show_distance_in,
      looking_up_skill_ids,
      designation_id,
      work_experience_id,
      gender,
      location_enabled,
      navigation_enabled,
      short_by,
      purpose,
      send_with_message,
    } = req.body;
    const profileSettings = await executeQuery(
      'CALL UpdateUserSettings(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        id,
        req.userData.id,
        swapping_in_location,
        profile_has_pitch_video,
        max_distance,
        age_range_from,
        age_range_to,
        show_me_on_techbuddy,
        dont_show_age,
        make_distance_invisible,
        show_distance_in,
        looking_up_skill_ids,
        designation_id,
        work_experience_id,
        gender,
        location_enabled,
        navigation_enabled,
        short_by,
        req.sessionId,
        purpose,
        send_with_message,
      ],
    );

    if (profileSettings[0].length > 0)
      response(
        res,
        200,
        'Profile Settings Updated  Successfully.',
        {
          ...profileSettings[0][0],
          location: profileSettings[2].length ? profileSettings[2][0] : {},
          looking_for_skills: groupByKey(profileSettings[1], 'master_skill'),
          subscription: profileSettings[3][0],
          features: profileSettings[4].length
            ? formatFeatures(profileSettings[4])
            : {},
        },
        true,
      );
    // 200 OK
    else response(res, 400, 'Profile Settings Fetched  Failed.', {}, false); // 200 OK
  } catch (error) {
    console.log(error, 'error');
    next(error);
  }
};

exports.insertUserLocation = async (req, res, next) => {
  try {
    const { name, latitude, longitude } = req.body;
    const profileSettings = await executeQuery(
      'CALL InsertUserLocation(?,?,?,?);',
      [req.userData.id, name, latitude, longitude],
    );
    if (profileSettings.affectedRows > 0)
      response(res, 200, 'Successful Updated Location', {}, true); // 200 OK
    else response(res, 400, 'Failed to Update Location', {}, false); // 200 OK
  } catch (error) {
    console.log(error, 'error');
    next(error);
  }
};

exports.insertUserSession = async (req, res, next) => {
  try {
    const { device_token, version, ip, fcm_token } = req.body;
    const { latitude, longitude, devicename } = req.headers;
    const data = await executeQuery(
      'CALL HandleUserSession(?,?,?,?,?,?,?,?,?);',
      [
        req.userData.id,
        device_token,
        devicename,
        fcm_token,
        version,
        ip,
        latitude,
        longitude,
        req.token,
      ],
    );
    if (data.affectedRows > 0)
      response(res, 200, ' Session Added Successfully', {}, true); // 200 OK
    else response(res, 400, 'Failed to Add Session', {}, false); // 200 OK
  } catch (error) {
    console.log(error, 'error');
    next(error);
  }
};

exports.verify = async (req, res, next) => {
  const { media_url = null } = req.body;
  try {
    const data = await executeQuery('CALL VerifyUserProfile(?,?);', [
      req.userData.id,
      media_url,
    ]);
    if (data.affectedRows > 0)
      response(res, 200, ' Successfully Profile Verified', {}, true); // 200 OK
    else response(res, 400, 'Failed to Added Devices', {}, false); // 200 OK
  } catch (error) {
    console.log(error, 'error');
    next(error);
  }
};

exports.getProfileDetailsById = async (req, res, next) => {
  try {
    const { id = null } = req.query;
    const userId = req.userData.id;

    const profileDetails = await executeQuery(
      'CALL GetProfileDetailsById(?,?)',
      [id, userId],
    );
    if (profileDetails[0].length > 0)
      response(
        res,
        200,
        'Profile Details Fetched  Successfully.',
        {
          ...profileDetails[0][0],

          skills: groupByKey(profileDetails[1], 'master_skill'),
          educations: profileDetails[2],
          experience: profileDetails[3],
          interest: profileDetails[4],
          languages: profileDetails[5],
          links: profileDetails[6],
        },
        true,
      );
    // 200 OK
    else response(res, 400, 'Profile Details Fetched  Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getInstitude = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetInstitute(?)', [search]);
    response(res, 200, 'Institude List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getDegreelist = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetAllDegrees(?)', [search]);
    response(res, 200, 'Degree List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getFieldList = async (req, res, next) => {
  try {
    const { search = null, degree_id } = req.query;
    const data = await executeQuery('CALL GetFieldOfStudy(?,?)', [
      search,
      degree_id == 0 ? null : degree_id,
    ]);
    response(res, 200, 'Degree List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getLanguageList = async (req, res, next) => {
  try {
    const { search = null } = req.query;

    const data = await executeQuery('CALL GetLanguageList(?,?)', [
      search,
      req.userData.id,
    ]);
    response(res, 200, 'Language List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getCompaniesList = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetCompaniesList(?)', [search]);
    response(res, 200, 'Companies List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getEmploymentTypeList = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetEmploymentType()');
    response(
      res,
      200,
      'Employment type List Fetched Successfully',
      data[0],
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.getLocationTypeList = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetLocationType()');
    response(
      res,
      200,
      'Job Location type List Fetched Successfully',
      data[0],
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addEducation = async (req, res, next) => {
  try {
    const {
      institution_id,
      institution_name,
      degree_id,
      degree_name,
      field_of_study_id,
      field_of_study_name,
      start_date,
      end_date,
      grade,
      description,
      is_current,
    } = req.body;
    const activity_id = await insertAction(req, 'Add Education');
    const data = await executeQuery(
      'CALL AddEducation(?,?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        institution_id,
        institution_name,
        degree_id,
        degree_name,
        field_of_study_id,
        field_of_study_name,
        start_date,
        end_date,
        grade,
        description,
        is_current,
        activity_id,
      ],
    );
    if (data.affectedRows)
      response(res, 200, 'Education Added  Successfully.', {}, true); // 200 OK
    else response(res, 400, 'Education Add  Failed.', {}, false); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.getUserRole = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetUserRole(?)', [search]);
    response(res, 200, 'User role List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.proficiencyLevel = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL ProficiencyLavel()');
    response(
      res,
      200,
      'Proficiency level List Fetched Successfully',
      data[0],
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};
exports.updateEducation = async (req, res, next) => {
  try {
    const {
      education_id,
      institution_id,
      institution_name,
      degree_id,
      degree_name,
      field_of_study_id,
      field_of_study_name,
      start_date,
      end_date,
      grade,
      description,
      is_current,
    } = req.body;
    const activity_id = await insertAction(req, 'Update Education');

    const data = await executeQuery(
      'CALL UpdateEducation(?,?,?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        education_id,
        institution_id,
        institution_name,
        degree_id,
        degree_name,
        field_of_study_id,
        field_of_study_name,
        start_date,
        end_date,
        grade,
        description,
        is_current,
        activity_id,
      ],
    );
    if (data.affectedRows)
      response(res, 200, 'Education Updated  Successfully.', {}, true); // 200 OK
    else response(res, 400, 'Education Add  Failed.', {}, false); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.deleteEducation = async (req, res, next) => {
  try {
    const { education_id } = req.query;
    const activity_id = await insertAction(req, 'Delete Education');
    const data = await executeQuery('CALL DeleteEducation(?,?,?)', [
      req.userData.id,
      education_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Education Deleted  Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Education Delete  Failed.', {}, false); // 200 OK
    }
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.getInterests = async (req, res, next) => {
  try {
    const { search } = req.query;
    const data = await executeQuery('CALL GetInterest(?)', [search]);
    response(res, 200, 'Interests List Fetched  Successfully.', data[0], true); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.addExperience = async (req, res, next) => {
  try {
    const {
      company_id,
      company_name,
      employment_type_id,
      position_id,
      position_name,
      location_name,
      location_type_id,
      start_date,
      end_date,
      is_current,
      latitude,
      longitude,
      description,
    } = req.body;
    const activity_id = await insertAction(req, 'Add Experience');

    const data = await executeQuery(
      'CALL AddExperience(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        company_id,
        company_name,
        employment_type_id,
        position_id,
        position_name,
        location_name,
        location_type_id,
        start_date,
        end_date,
        is_current,
        latitude,
        longitude,
        description,
        activity_id,
      ],
    );
    if (data.affectedRows)
      response(res, 200, 'Experience  Added  Successfully.', {}, true); // 200 OK
    else response(res, 400, 'Experience Add  Failed.', {}, false); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.updateExperience = async (req, res, next) => {
  try {
    const {
      experience_id,
      company_id,
      company_name,
      employment_type_id,
      position_id,
      position_name,
      location_name,
      location_type_id,
      start_date,
      end_date,
      is_current,
      description,
      latitude,
      longitude,
    } = req.body;
    const activity_id = await insertAction(req, 'Update Experience');
    console.log(activity_id, 'activity_id');
    const data = await executeQuery(
      'CALL UpdateExperience(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)',
      [
        req.userData.id,
        experience_id,
        company_id,
        company_name,
        employment_type_id,
        position_id,
        position_name,
        location_name,
        location_type_id,
        start_date,
        end_date,
        is_current,
        description,
        latitude,
        longitude,
        activity_id,
      ],
    );
    if (data.affectedRows)
      response(res, 200, 'Experience  update  Successfully.', {}, true); // 200 OK
    else response(res, 400, 'Experience update  Failed.', {}, false); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.addInterests = async (req, res, next) => {
  try {
    const { interest_ids } = req.body;
    const activity_id = await insertAction(req, 'Add Interests');
    await executeQuery('CALL AddInterests(?,?,?)', [
      req.userData.id,
      interest_ids,
      activity_id,
    ]);
    response(res, 200, 'Interests List Fetched  Successfully.', {}, true); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.addOtherInterest = async (req, res, next) => {
  try {
    const { interest_name } = req.body;
    const activity_id = await insertAction(req, 'Add Other Interest');
    const data = await executeQuery('CALL AddOtherInterests(?,?,?)', [
      req.userData.id,
      interest_name,
      activity_id,
    ]);
    if (data[0].length) {
      if (data[0][0].result) {
        response(res, 409, 'Interest is already exist.', {}, false); // 200 OK
      } else
        response(res, 200, 'Interest Added Successfully.', data[0][0], true); //
    } else {
      response(res, 400, 'Interest Add Failed.', data[0], true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.deleteOtherInterest = async (req, res, next) => {
  try {
    const { id } = req.query;
    const activity_id = await insertAction(req, 'Delete Other Interest');
    const data = await executeQuery('CALL DeleteOtherInterests(?,?,?)', [
      req.userData.id,
      id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Interests Deleted  Successfully.', data[0], true); // 200 OK
    } else {
      response(res, 400, 'Interests Not Deleted.', data[0], false); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.deleteExperience = async (req, res, next) => {
  try {
    const { experience_id } = req.query;
    const activity_id = await insertAction(req, 'Delete Experience');

    const data = await executeQuery('CALL DeleteExperience(?,?,?)', [
      req.userData.id,
      experience_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Experience Deleted  Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Experience Delete  Failed.', {}, false); // 200 OK
    }
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.addLanguage = async (req, res, next) => {
  try {
    const { user_language_id, language_id, proficiency_id } = req.body;
    const activity_id = await insertAction(req, 'Add Language');

    const data = await executeQuery('CALL AddLanguages(?,?,?,?,?)', [
      req.userData.id,
      language_id,
      proficiency_id,
      user_language_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Language  Added Successfully.', data[0], true); //
    } else {
      response(res, 400, 'language Add Failed.', data[0], true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.updateLanguage = async (req, res, next) => {
  try {
    const { user_language_id = null, language_id, proficiency_id } = req.body;
    const activity_id = await insertAction(req, 'Update Language');

    const data = await executeQuery('CALL AddLanguages(?,?,?,?,?)', [
      req.userData.id,
      language_id,
      proficiency_id,
      user_language_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Language  update Successfully.', data[0], true); //
    } else {
      response(res, 400, 'language update Failed.', data[0], true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.deleteLanguage = async (req, res, next) => {
  try {
    const { user_language_id } = req.query;
    const activity_id = await insertAction(req, 'Delete Language');
    const data = await executeQuery('CALL DeleteLanguage(?,?,?)', [
      req.userData.id,
      user_language_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'Language Deleted  Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Language Delete  Failed.', {}, false); // 200 OK
    }
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.updateSeqance = async (req, res, next) => {
  try {
    const shortedArr = req.body
      .sort((a, b) => a.order_id - b.order_id)
      .map((i) => i.experience_id)
      .join(',');

    const activity_id = await insertAction(req, 'update sequance');
    const seq = await executeQuery('CALL UpdateExperianceSeq(?,?,?)', [
      req.userData.id,
      shortedArr,
      activity_id,
    ]);
    if (seq.affectedRows) {
      response(res, 200, 'Sequance update  Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'Sequance update Failed.', {}, true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.addLinks = async (req, res, next) => {
  try {
    const { link_type, link, link_id = null } = req.body;
    const activity_id = await insertAction(req, 'Add Links');
    let data = await executeQuery('CALL AddLinks(?,?,?,?,?)', [
      req.userData.id,
      link_id,
      link_type,
      link,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'links added Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'links Failed.', {}, true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.listLinkType = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetLinkType(?)', [req.userData.id]);
    response(res, 200, 'Link type list Successfully fetched.', data[0], true); //
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.deleteLinks = async (req, res, next) => {
  try {
    const { link_id } = req.query;
    const activity_id = await insertAction(req, 'delete Links');
    let data = await executeQuery('CALL DeleteLinks(?,?,?)', [
      req.userData.id,
      link_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'links deleted Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'links deleted Failed.', {}, true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.updateLinks = async (req, res, next) => {
  try {
    const { link_type, link, link_id } = req.body;
    const activity_id = await insertAction(req, 'update Links');
    let data = await executeQuery('CALL AddLinks(?,?,?,?,?)', [
      req.userData.id,
      link_id,
      link_type,
      link,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, 'links updated Successfully.', {}, true); // 200 OK
    } else {
      response(res, 400, 'links update Failed.', {}, true); //
    }
  } catch (error) {
    console.log();
    next(error);
  }
};
exports.listMedia = async (req, res, next) => {
  try {
    const { type } = req.query;
    const data = await executeQuery('CALL GetMedia(?,?)', [
      req.userData.id,
      type,
    ]);

    response(res, 200, 'Media list Successfully.', data[0], true); //
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.updateMedia = async (req, res, next) => {
  try {
    const { media_id, type } = req.body;
    const data = await executeQuery('CALL UpdateMedia(?,?,?)', [
      req.userData.id,
      media_id,
      type,
    ]);

    if (data.affectedRows > 0)
      response(res, 200, 'Successful Updated Media', {}, true); // 200 OK
    else response(res, 400, 'Failed to Update Media', {}, false); // 200 OK
  } catch (error) {
    console.log();
    next(error);
  }
};

exports.matchedPercentage = async (req, res, next) => {
  try {
    const { other_user_id } = req.body;
    const data = await executeQuery('CALL new_matched_percentage(?,?)', [
      req.userData.id,
      other_user_id,
    ]);
    console.log(data[4][0].pitch_score, 'data[4][0].pitch_score');
    response(
      res,
      200,
      'Successful ',
      {
        total_percentage: data[4][0].total_percentage,
        match_percentage: data[4][0].match_percentage,
        skills: {
          list: data[0],
          percentage: data[4][0].skill_percentage,
          total: data[4][0].skill_score,
        },
        education: {
          list: data[1],
          percentage: data[4][0].education_percentage,
          total: data[4][0].education_score,
        },
        experience: {
          // Fixed typo from 'experiance' to 'experience'
          list: data[3],
          percentage: data[4][0].experience_percentage,
          total: data[4][0].experience_score,
        },
        interest: {
          list: data[2],
          percentage: data[4][0].interest_percentage,
          total: data[4][0].interest_score,
        },
        distance: {
          distance: data[4][0].distance,
          percentage: data[4][0].distance_percentage,
          total: data[4][0].distance_score,
        },
        profilePitch: {
          pitchAdded: data[4][0].pitch_score == 0 ? false : true,
          percentage: data[4][0].pitch_percentage,
          total: data[4][0].pitch_score,
        },
      },
      true,
    ); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.deletePitch = async (req, res, next) => {
  try {
    await executeQuery('CALL DeletePitch(?)', [req.userData.id]);
    response(res, 200, 'Successful ', {}, true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.addPhoneAndCode = async (req, res, next) => {
  try {
    const { phone_num, country_code } = req.body;

    await executeQuery('CALL AddPhoneCountryCode(?,?,?)', [
      req.userData.id,
      phone_num,
      country_code,
    ]);
    response(res, 200, 'Phone number  save successfully.', {}, true); // 200 OK
  } catch (error) {
    next(error);
  }
};

const storeGithubProfile = async (userId, uniqueCode, githubData) => {
  try {
    const query =
      'CALL InsertOrUpdateGithubProfile(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';

    return await executeQuery(query, [
      userId || null, // Optional
      uniqueCode, // Required
      githubData.id, // Required
      githubData.login, // Required
      githubData.name, // Required
      githubData.avatar_url || null,
      githubData.bio || null,
      githubData.blog || null,
      githubData.company || null,
      githubData.created_at || null,
      githubData.email || null,
      githubData.followers || null,
      githubData.following || null,
      githubData.public_gists || null,
      githubData.public_repos || null,
      githubData.hireable === null ? null : !!githubData.hireable,
      githubData.location || null,
      githubData.html_url || null,
      githubData.twitter_username || null,
      githubData.type || null,
      githubData.site_admin === null ? null : !!githubData.site_admin,
      githubData.updated_at || null,
      githubData.url || null,
      githubData.organizations_url || null,
      githubData.repos_url || null,
      githubData.events_url || null,
      githubData.received_events_url || null,
      githubData.starred_url || null,
      githubData.subscriptions_url || null,
      githubData.gists_url || null,
      githubData.followers_url || null,
      githubData.following_url || null,
      githubData.gravatar_id || null,
      githubData.notification_email || null,
      githubData.user_view_type || null,
    ]);
  } catch (error) {
    console.error('Error storing GitHub profile:', error);
  }
};

exports.syncGithub = async (req, res, next) => {
  try {
    const { code, githubProfile } = req.body;
    const { id } = req.userData;
    const data = await storeGithubProfile(id, code, githubProfile);
    if (data[0] && data[0][0]) {
      const { status_code, status, bio, avatar, name } = data[0][0];
      response(res, status_code, status, { bio, avatar, name }, true);
    } else {
      response(res, 400, 'Failed to sync GitHub profile', {}, false);
    }
  } catch (error) {
    next(error);
  }
};

exports.unsyncGithub = async (req, res, next) => {
  try {
    const { id } = req.userData;
    const data = await executeQuery('call UnsyncGithub(?)', [id]);
    response(res, 200, data[0][0].status, {}, true);
  } catch (error) {
    next(error);
  }
};

// exports.getProfileDetailsList = async (req, res, next) => {
//   try {
//     const { ids = '' } = req.body;

//     // Convert comma-separated string to array of numbers
//     const idArray = ids.split(',').map((id) => parseInt(id.trim(), 10));

//     // Use Promise.all to handle async operations properly
//     const profilePromises = idArray.map(async (item) => {
//       const profileDetails = await executeQuery('CALL GetProfileDetails(?)', [
//         item,
//       ]);

//       // Check if profile exists
//       if (profileDetails[0].length === 0) {
//         return null; // or handle missing profile as needed
//       }

//       return {
//         ...profileDetails[0][0],
//         skills: groupByKey(profileDetails[1], 'master_skill'),
//         educations: profileDetails[2],
//         experience: profileDetails[3],
//         interest: profileDetails[4],
//         languages: profileDetails[5],
//         links: profileDetails[6],
//       };
//     });

//     // Wait for all promises to resolve
//     const results = await Promise.all(profilePromises);

//     // Filter out null results (missing profiles)
//     const data = results.filter((profile) => profile !== null);

//     response(res, 200, 'Profile Details Fetched Successfully.', data, true);
//   } catch (error) {
//     next(error);
//   }
// };
