const { RtcTokenBuilder, RtcRole } = require('agora-token');
const { executeQuery, response } = require('../config/dbConfig');
const { insertAction, globalVar } = require('../utils');
const { v4: uuidv4 } = require('uuid');
const { sendVoipNotification } = require('../config/apnConfig');
const { sendChatNotification } = require('./notification');
const APP_ID = process.env.AGORA_APP_ID;
const APP_CERTIFICATE = process.env.AGORA_APP_CERTIFICATE;

exports.reportUser = async (req, res, next) => {
  try {
    const { report_user_id, report_reason_id, description, attachment_link } =
      req.body;
    const userId = req.userData.id;
    let outer;
    const activity_id = await insertAction(req, 'Report user', outer);

    const data = await executeQuery('CALL ReportUser(?,?,?,?,?,?)', [
      userId,
      report_user_id,
      report_reason_id,
      description,
      attachment_link,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Report User successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to repot user', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.getReportReason = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetReportReasons()', []);
    response(res, 200, 'Report List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.blockUser = async (req, res, next) => {
  try {
    const { block_user_id } = req.body;
    const userId = req.userData.id;
    const activity_id = await insertAction(req, 'Block user');
    const data = await executeQuery('CALL BlockUser(?,?,?)', [
      userId,
      block_user_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Block  User successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to repot user', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.getBlockList = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const data = await executeQuery('CALL GetBlockList(?)', [userId]);
    response(res, 200, 'Block List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.matchList = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { page_number = 1, page_size = 10 } = req.body;

    // Validate pagination parameters
    const validPageNumber = Math.max(1, parseInt(page_number, 10));
    const validPageSize = Math.min(100, Math.max(1, parseInt(page_size, 10))); // Limit max page size to 100

    const data = await executeQuery('CALL GetUserMatchList(?,?,?)', [
      userId,
      validPageSize,
      validPageNumber,
    ]);

    response(
      res,
      200,
      'Matched List Fetched Successfully',
      {
        total_likes: parseInt(data[0][0]?.total_likes, 10) || 0,
        match_list: data[1] ?? [],
        total_pages: parseInt(data[2][0]?.total_pages, 10) || 0,
      },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.unblockUser = async (req, res, next) => {
  try {
    const { unblock_user_id } = req.body;
    const userId = req.userData.id;
    const activity_id = await insertAction(req, 'Unblock user');
    const data = await executeQuery('CALL UnBlockUser(?,?,?)', [
      userId,
      unblock_user_id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 201, 'Unblock  User successfully.', {}, true); // 201 Created
    } else {
      response(res, 400, 'Failed to unblock user', {}, false); // 409 Conflict
    }
  } catch (error) {
    next(error);
  }
};

exports.startChat = async (req, res, next) => {
  try {
    const { match_id } = req.body;
    const activity_id = await insertAction(req, 'Start Chat');
    const result = await executeQuery('call StartChat(?,?)', [
      match_id,
      activity_id,
    ]);
    if (result.affectedRows) {
      response(res, 200, 'Successfull', {}, true); // 201 Created
    } else {
      response(res, 400, 'failed', {}, true); // 201 Created
    }
  } catch (error) {
    next(error);
  }
};

exports.unMatch = async (req, res, next) => {
  try {
    const { match_id, reason } = req.body;
    const activity_id = await insertAction(req, 'UnMatch');
    await executeQuery('CALL RemoveMatch(?,?,?,?)', [
      match_id,
      req.userData.id,
      activity_id,
      reason,
    ]);
    response(res, 200, 'Successfull', {}, true);
  } catch (error) {
    next(error);
  }
};

exports.removeLikeSuperLike = async (req, res, next) => {
  try {
    const { liked_id } = req.body;

    await executeQuery('CALL RemoveLike(?)', [liked_id]);
    response(res, 200, 'Successfully Remove like', {}, true);
  } catch (error) {
    next(error);
  }
};

exports.likeList = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { page_number, page_size } = req.body;
    const data = await executeQuery('CALL UsersLikeList(?,?,?)', [
      userId,
      page_number,
      page_size,
    ]);
    response(
      res,
      200,
      'Liked List Fetched Successfully',
      { like_list: data[0], total_pages: data[0][0]?.total_pages || 0 },
      true,
    );
  } catch (error) {
    next(error);
  }
};

exports.viewPitch = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { pitch_id, duration, city, state, country, area } = req.body;
    const { devicename, latitude, longitude, ip } = req.headers;
    const activityInsertStatus = await executeQuery(
      'CALL AddUserActivity(?,?,?,?,?,?)',
      [userId, 'View Pitch', ip, latitude, longitude, devicename],
    );
    if (activityInsertStatus[0].length) {
      const { activity_id } = activityInsertStatus[0][0];
      const pitchstatus = await executeQuery(
        'CALL AddPitchStats(?,?,?,?,?,?,?,?,?,?)',
        [
          pitch_id,
          userId,
          activity_id,
          duration,
          latitude,
          longitude,
          city,
          state,
          country,
          area,
        ],
      );
      if (pitchstatus[0].length) {
        response(res, 200, 'Pitch Stats Added Successfully !', {}, true);
      } else {
        response(res, 400, 'Failed To Add Pitch Stats', {}, false);
      }
    }
  } catch (error) {
    next(error);
  }
};

exports.viewPitchDetails = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { pitch_id, start_date, end_date } = req.body;
    const { devicename, latitude, longitude, ip } = req.headers;
    // Parse start_date and end_date as Date objects
    const startDateObj = new Date(start_date);
    const endDateObj = new Date(end_date);
    // const prevWeekStartDates = new Date(
    //   startDateObj.setDate(startDateObj.getDate()+7),
    // );
    // Subtract 7 days to get the previous week's start and end dates
    const prevWeekStartDate = new Date(
      startDateObj.setDate(startDateObj.getDate()),
    );
    const prevWeekStartDates = new Date(
      startDateObj.setDate(startDateObj.getDate() + 7),
    );
    const prevWeekEndDate = new Date(
      endDateObj.setDate(endDateObj.getDate() - 7),
    );

    // Format the dates to YYYY-MM-DD
    const formattedPrevWeekStartDate = prevWeekStartDate
      .toISOString()
      .split('T')[0];
    const formattedPrevWeekEndDate = prevWeekEndDate
      .toISOString()
      .split('T')[0];
    const prevweekData = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
      pitch_id,
      formattedPrevWeekStartDate,
      formattedPrevWeekEndDate,
    ]);

    var date = new Date();

    console.log(date.getDay(), ' date.getDay()');
    // let CurrentWeekStart = new Date(date.setDate(date.getDate()));

    let WeekStart = date.getDate() - date.getDay() - 13;
    let WeekFrom = new Date(date.setDate(WeekStart));
    let WeekEnd = date.getDate() - date.getDay() + 14;

    let CurrentWeekStart = date.getDate() - date.getDay() + 7;
    let CurrentWeekStartWeekTo = new Date(date.setDate(CurrentWeekStart));
    let WeekTo = new Date(date.setDate(WeekEnd));
    let formattedlastWeekStart = WeekFrom.toISOString().split('T')[0];
    let formattedlastWeekEnd = WeekTo.toISOString().split('T')[0];
    let formattedCurrentWeek =
      CurrentWeekStartWeekTo.toISOString().split('T')[0];
    const prevweekDatas = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
      pitch_id,
      formattedlastWeekStart,
      formattedCurrentWeek,
    ]);
    const currentWeek = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
      pitch_id,
      formattedCurrentWeek,
      formattedlastWeekEnd,
    ]);

    console.log(formattedPrevWeekStartDate, formattedPrevWeekEndDate, 'ppppp');
    console.log(prevWeekStartDates, end_date, 'cccccc');

    const activityInsertStatus = await executeQuery(
      'CALL AddUserActivity(?,?,?,?,?,?)',
      [userId, 'View Pitch Details', ip, latitude, longitude, devicename],
    );
    if (activityInsertStatus[0].length) {
      const pitchDetails = await executeQuery('CALL ViewPitchDetails(?,?,?)', [
        pitch_id,
        prevWeekStartDates,
        end_date,
      ]);

      // console.log(pitchDetails[2],'pitch percentage')
      response(
        res,
        200,
        'Pitch Details !',
        {
          pitchDetails: pitchDetails[1],
          viewersCount: pitchDetails[0][0].total_view_count,
          overallPercentage: globalVar.calculatePercentageChange(
            currentWeek[0][0].total_view_count,
            prevweekDatas[0][0].total_view_count,
          ),
          viewStats: globalVar.calculateViewCountChange(
            globalVar.formatData(pitchDetails[2]),
            globalVar.formatData(prevweekData[2]),
          ),
        },
        true,
      );
    }
  } catch (error) {
    next(error);
  }
};
exports.userSearch = async (req, res, next) => {
  try {
    const { search } = req.body;
    const data = await executeQuery('CALL UserSearch(?,?)', [
      search,
      req.userData.id,
    ]);
    response(res, 200, 'Successfull', data[0][0], true);
  } catch (error) {
    next(error);
  }
};

exports.revertLastAction = async (req, res, next) => {
  try {
    const activity_id = await insertAction(req, 'Revert Last Action');

    const data = await executeQuery('CALL Rewind(?,?)', [
      req.userData.id,
      activity_id,
    ]);
    if (data.affectedRows) {
      response(res, 200, ' Rewind Successfully', {}, true);
    } else {
      response(res, 400, 'Failed to Rewind ', {}, false);
    }
  } catch (error) {
    next(error);
  }
};
const generateChannelName = () => {
  const timestamp = Date.now(); // Get current timestamp
  const uniqueName = `channel-${timestamp}-${uuidv4()}`; // Combine timestamp with UUID
  return uniqueName;
};

const getVoipTokenForUser = async (userId) => {
  const result = await executeQuery('call GetUserVoipDetails(?)', [userId]);

  return result[0].length
    ? {
        voip_token: result[0][0].voip_token,
        full_name: result[0][0].full_name,
        profile_pic: result[0][0].profile_pic,
      }
    : null;
};

exports.accessTokenGenerate = async (req, res, next) => {
  try {
    const channelName = generateChannelName();
    const uid = req.userData.id; // Currently logged-in user
    const uid2 = req.query.id; // ID of the receiver

    const voipInfo = await getVoipTokenForUser(uid2);
    if (voipInfo?.voip_token) {
      const notificationPayload = {
        channelName,
        callerId: uid,
        type: 'incoming_call',
        timestamp: Date.now(),
        callType: 'video',
        callerPic: voipInfo.profile_pic,
        sender_name: voipInfo.full_name,
      };

      try {
        await sendVoipNotification(voipInfo.voip_token, notificationPayload);
      } catch (notifyErr) {
        console.warn('⚠️ Failed to send VoIP notification:', notifyErr);
      }
    } else {
      await sendChatNotification({
        senderId: uid,
        receiverId: uid2,
        message: 'Incoming Video Call',
        type: 'Call',
      });
    }
    const role = RtcRole.PUBLISHER;
    const expirationTimeInSeconds = 3600; // 1 hour
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const privilegeExpiredTs = currentTimestamp + expirationTimeInSeconds;

    // Generate token for the first user (uid)
    const token1 = RtcTokenBuilder.buildTokenWithUid(
      APP_ID,
      APP_CERTIFICATE,
      channelName,
      uid,
      role,
      privilegeExpiredTs,
    );

    // Generate token for the second user (uid2)
    const token2 = RtcTokenBuilder.buildTokenWithUid(
      APP_ID,
      APP_CERTIFICATE,
      channelName,
      uid2,
      role,
      privilegeExpiredTs,
    );

    // Call the stored procedure to insert or update the video call history
    const callId = await executeQuery(
      'CALL InsertVideoCallHistory(?, ?, ?, ?, ?)',
      [channelName, uid, uid2, token1, token2],
    );
    // Respond with both tokens and channel info
    response(
      res,
      200,
      'Tokens Created Successfully!',
      {
        token1, // Token for the first user
        token2, // Token for the second user
        channelName, // Same channel for both users
        expirationTimeInSeconds,
        callId: callId[0][0].inserted_id,
      },
      true,
    );
  } catch (error) {
    console.error('Error generating access tokens:', error);
    next(error);
  }
};

exports.updateVideoCallHistory = async (req, res, next) => {
  try {
    const { call_id, start_time, end_time, status } = req.body;
    await executeQuery('CALL UpdateVideoCallHistory(?,?,?,?)', [
      call_id,
      start_time,
      end_time,
      status,
    ]);
    response(res, 200, 'Video Call History Updated Successfully!', {}, true);
  } catch (error) {
    console.error('Error updating video call history:', error);
    next(error);
  }
};

exports.blockStatus = async (req, res, next) => {
  try {
    const userId = req.userData.id;
    const { other_user_id } = req.query;
    const data = await executeQuery('CALL GetUserBlockedStatus(?,?)', [
      userId,
      other_user_id,
    ]);
    response(res, 200, 'Block status Fetched Successfully', data[0][0], true);
  } catch (error) {
    next(error);
  }
};
