const { executeQuery, response } = require('../config/dbConfig');

exports.getExperience = async (req, res, next) => {
  try {
    const data = await executeQuery('CALL GetExperience()', []);
    response(res, 200, 'Experience List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};

exports.getDesignation = async (req, res, next) => {
  try {
    const { search = null } = req.query;
    const data = await executeQuery('CALL GetDesignations(?)', [search]);
    response(res, 200, 'Experience List Fetched Successfully', data[0], true); // 200 OK
  } catch (error) {
    next(error);
  }
};
