const { default: axios } = require('axios');

const mailer = require('../config/mailerConfig');
const { mailContactUs } = require('../utils/mailhelpers');
const { executeQuery, response } = require('../config/dbConfig');
const secretKey = process.env.CAPTCHA_SECRET;

exports.validateAndSendContactForm = async (req, res, next) => {
  try {
    const { token, email, jobTitle, fullname, phone, message, userType } =
      req.body;

    const captchaResponse = await axios.post(
      'https://www.google.com/recaptcha/api/siteverify',
      null,
      {
        params: {
          secret: secretKey,
          response: token,
        },
      },
    );

    if (!captchaResponse.data.success) {
      return res
        .status(400)
        .json({ success: false, message: 'CAPTCHA verification failed' });
    }

    await executeQuery('CALL AddContactUsData(?,?,?,?,?,?);', [
      email,
      fullname,
      phone,
      jobTitle,
      message,
      userType,
    ]);

    // ✅ 3. Send email notification
    mailer.sendMail(
      mailContactUs(fullname, email, jobTitle, phone, message, userType),
      (err, info) => {
        if (err) {
          console.error('Error sending mail:', err);
        } else {
          console.log('Mail sent:', info.response);
        }
      },
    );

    // ✅ 4. Success response
    res
      .status(200)
      .json({
        success: true,
        message: 'Form submitted and email sent successfully',
      });
  } catch (error) {
    next(error);
  }
};
exports.validateCaptcha = async (req, res, next) => {
  try {
    const token = req.body.token;
    const responseData = await axios.post(
      'https://www.google.com/recaptcha/api/siteverify',
      null,
      {
        params: {
          secret: secretKey,
          response: token,
        },
      },
    );

    const { success } = responseData.data;
    if (!success) {
      response(res, 400, 'CAPTCHA verification failed', {}, false); //
    } else {
      response(res, 200, 'CAPTCHA validate!.', {}, true); // 200 OK
    }
  } catch (error) {
    next(error);
  }
};
