const moment = require('moment');

// Platform-specific status maps
const androidSubscriptionStatusMap = {
  SUBSCRIPTION_PURCHASED: {
    status: 'active',
    metadata: {
      purchaseDate: '${event.timestamp}',
      paymentSource: 'android',
      purchaseToken: '${event.data.purchaseToken}',
    },
  },
  SUBSCRIPTION_CANCELED: {
    status: 'canceled',
    metadata: {
      cancelDate: '${event.timestamp}',
      purchaseToken: '${event.data.purchaseToken}',
    },
  },
  SUBSCRIPTION_RENEWED: {
    status: 'active',
    metadata: {
      renewalDate: '${event.timestamp}',
      expiryDate: '${event.data.expiryDate}',
      purchaseToken: '${event.data.purchaseToken}',
    },
  },
  SUBSCRIPTION_IN_GRACE_PERIOD: {
    status: 'grace_period',
    metadata: {
      gracePeriodStart: '${event.timestamp}',
      gracePeriodEnd: '${event.data.gracePeriodEnd}',
      purchaseToken: '${event.data.purchaseToken}',
    },
  },
};

const iosSubscriptionStatusMap = {
  INITIAL_BUY: {
    status: 'active',
    metadata: {
      purchaseDate: '${event.timestamp}',
      paymentSource: 'ios',
      transactionId: '${event.data.transactionId}',
    },
  },
  CANCEL: {
    status: 'canceled',
    metadata: {
      cancelDate: '${event.timestamp}',
      transactionId: '${event.data.transactionId}',
    },
  },
  RENEWAL: {
    status: 'active',
    metadata: {
      renewalDate: '${event.timestamp}',
      expiryDate: '${event.data.expiryDate}',
      transactionId: '${event.data.transactionId}',
    },
  },
  DID_CHANGE_RENEWAL_STATUS: {
    status: 'active',
    metadata: {
      statusChangeDate: '${event.timestamp}',
      transactionId: '${event.data.transactionId}',
      autoRenewStatus: '${event.data.autoRenewStatus}',
    },
  },
};

const paymentStatusMap = {
  PAYMENT_SUCCEEDED: {
    status: 'paid',
    metadata: {
      paymentDate: '${event.timestamp}',
      amount: '${event.data.amount}',
      currency: '${event.data.currency}',
    },
  },
  PAYMENT_FAILED: {
    status: 'failed',
    metadata: {
      failureDate: '${event.timestamp}',
      failureReason: '${event.data.reason}',
    },
  },
  PAYMENT_REFUNDED: {
    status: 'refunded',
    metadata: {
      refundDate: '${event.timestamp}',
      refundAmount: '${event.data.amount}',
    },
  },
};

// Helper function to interpolate values in metadata
const interpolateMetadata = (metadata, event) => {
  const interpolated = {};
  for (const [key, value] of Object.entries(metadata)) {
    if (
      typeof value === 'string' &&
      value.startsWith('${') &&
      value.endsWith('}')
    ) {
      const path = value.slice(2, -1).split('.');
      let result = event;
      for (const part of path) {
        result = result?.[part];
      }
      interpolated[key] = result;
    } else {
      interpolated[key] = value;
    }
  }
  return interpolated;
};

// Helper function to create status update rules
const createStatusRules = (statusMap) => {
  return Object.entries(statusMap).map(([eventType, config]) => ({
    condition: (event) => event.type === eventType,
    update: {
      status: config.status,
      metadata: (event) => interpolateMetadata(config.metadata, event),
    },
  }));
};

// Platform-specific field transformations
const androidFieldTransformations = {
  expiry_date: (event) => {
    if (event.data.expiryDate) {
      return moment(event.data.expiryDate).format('YYYY-MM-DD HH:mm:ss');
    }
    return null;
  },
  payment_status_id: (event) => {
    const statusIdMap = {
      SUBSCRIPTION_PURCHASED: 2,
      SUBSCRIPTION_CANCELED: 5,
      SUBSCRIPTION_RENEWED: 2,
      SUBSCRIPTION_IN_GRACE_PERIOD: 9,
    };
    return statusIdMap[event.type] || null;
  },
  purchase_token: (event) => event.data.purchaseToken,
};

const iosFieldTransformations = {
  expiry_date: (event) => {
    if (event.data.expiryDate) {
      return moment(event.data.expiryDate).format('YYYY-MM-DD HH:mm:ss');
    }
    return null;
  },
  payment_status_id: (event) => {
    const statusIdMap = {
      INITIAL_BUY: 2,
      CANCEL: 5,
      RENEWAL: 2,
      DID_CHANGE_RENEWAL_STATUS: 2,
    };
    return statusIdMap[event.type] || null;
  },
  transaction_id: (event) => event.data.transactionId,
};

// Export the configuration
module.exports = {
  subscription: {
    android: {
      table: 'user_subscriptions',
      updateRules: createStatusRules(androidSubscriptionStatusMap),
      additionalFields: androidFieldTransformations,
    },
    ios: {
      table: 'user_subscriptions',
      updateRules: createStatusRules(iosSubscriptionStatusMap),
      additionalFields: iosFieldTransformations,
    },
  },
  payment: {
    table: 'payments',
    updateRules: createStatusRules(paymentStatusMap),
    additionalFields: {
      transaction_date: (event) =>
        moment(event.timestamp).format('YYYY-MM-DD HH:mm:ss'),
      payment_method: (event) => event.source,
    },
  },
};
