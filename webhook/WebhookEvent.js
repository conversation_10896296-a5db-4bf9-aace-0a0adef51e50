class WebhookEvent {
  constructor(eventData) {
    this.eventData = eventData;
    this.timestamp = new Date();
  }

  getEventType() {
    return this.eventData.type;
  }

  getEventData() {
    return this.eventData;
  }

  getTimestamp() {
    return this.timestamp;
  }

  getPayload() {
    return this.eventData.payload || this.eventData;
  }

  getSource() {
    return this.eventData.source;
  }

  getIdentifier() {
    return this.eventData.identifier || this.eventData.id;
  }

  toJSON() {
    return {
      type: this.getEventType(),
      data: this.getEventData(),
      timestamp: this.getTimestamp(),
      source: this.getSource(),
      identifier: this.getIdentifier(),
    };
  }
}

module.exports = { WebhookEvent };
