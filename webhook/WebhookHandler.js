const { WebhookEvent } = require('./WebhookEvent');
const statusConfig = require('./statusConfig');

class WebhookHandler {
  constructor(config) {
    this.validateConfig(config);
    this.db = config.getDbConfig();
    this.webhookType = config.getWebhookType();
    this.platform = config.getPlatform();
    this.notificationConfig = config.getNotificationConfig();

    // Get the specific configuration for this webhook type and platform
    const typeConfig = statusConfig[this.webhookType];
    if (!typeConfig) {
      throw new Error(
        `No configuration found for webhook type: ${this.webhookType}`,
      );
    }

    const platformConfig = typeConfig[this.platform];
    if (!platformConfig) {
      throw new Error(`No configuration found for platform: ${this.platform}`);
    }

    this.tableName = platformConfig.table;
    this.updateRules = platformConfig.updateRules;
    this.additionalFields = platformConfig.additionalFields || {};
  }

  validateConfig(config) {
    if (!config.getDbConfig()) {
      throw new Error('Database configuration is required');
    }
    if (!config.getWebhookType()) {
      throw new Error('Webhook type is required');
    }
    if (!config.getPlatform()) {
      throw new Error('Platform is required');
    }
  }

  async handleEvent(eventData) {
    try {
      const event = new WebhookEvent(eventData);
      // await this.logEvent(event);

      const statusUpdate = this.getStatusUpdate(event);
      // if (statusUpdate) {
      //   await this.updateStatus(event, statusUpdate);
      // }

      if (this.notificationConfig?.enabled) {
        await this.sendNotification(event, statusUpdate);
      }

      return {
        success: true,
        event: event.toJSON(),
        statusUpdate,
      };
    } catch (error) {
      console.error('Error handling webhook event:', error);
      throw error;
    }
  }

  // async logEvent(event) {
  //   // const query = `
  //   //         INSERT INTO ${this.tableName}_logs (

  //   //             event_data,
  //   //             source,
  //   //             platform,
  //   //             identifier,
  //   //             timestamp
  //   //         ) VALUES ( ?, ?, ?, ?, ?)
  //   //     `;

  //   // const values = [
  //   //   event.getEventType(),
  //   //   JSON.stringify(event.getEventData()),
  //   //   event.getSource(),
  //   //   this.platform,
  //   //   event.getIdentifier(),
  //   //   event.getTimestamp(),
  //   // ];

  //   // await this.db.executeQuery(query, values);
  // }

  getStatusUpdate(event) {
    const eventData = event.toJSON();

    // Find the first matching rule
    const matchingRule = this.updateRules.find((rule) =>
      rule.condition(eventData),
    );
    if (!matchingRule) {
      return null;
    }

    const { status, metadata } = matchingRule.update;
    return {
      status,
      metadata: typeof metadata === 'function' ? metadata(eventData) : metadata,
      additionalFields: this.getAdditionalFields(eventData),
    };
  }

  getAdditionalFields(eventData) {
    const fields = {};
    for (const [field, getter] of Object.entries(this.additionalFields)) {
      const value = getter(eventData);
      if (value !== null && value !== undefined) {
        fields[field] = value;
      }
    }
    return fields;
  }

  async updateStatus(event, statusUpdate) {
    const { status, metadata, additionalFields } = statusUpdate;

    // Build the SET clause
    const setClause = [
      'status = ?',
      'metadata = JSON_MERGE_PATCH(COALESCE(metadata, "{}"), ?)',
      'updated_at = NOW()',
    ];

    // Add additional fields to SET clause
    const values = [status, JSON.stringify(metadata)];

    for (const [field, value] of Object.entries(additionalFields)) {
      setClause.push(`${field} = ?`);
      values.push(value);
    }

    const query = `
            UPDATE ${this.tableName}
            SET ${setClause.join(', ')}
            WHERE identifier = ?
        `;

    values.push(event.getIdentifier());
    await this.db.executeQuery(query, values);
  }

  async sendNotification(event, statusUpdate) {
    if (!this.notificationConfig?.handler) {
      return;
    }

    const notificationData = {
      event: event.toJSON(),
      statusUpdate,
      config: this.notificationConfig,
      platform: this.platform,
    };

    await this.notificationConfig.handler(notificationData);
  }
}

module.exports = { WebhookHandler };
