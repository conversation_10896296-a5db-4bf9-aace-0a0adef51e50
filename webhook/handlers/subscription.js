const { WebhookHandler } = require('../core/WebhookHandler');
const { validateWebhookPayload } = require('../../validators/subscription');
const {
  getSubscriptionStatus,
  updateSubscriptionStatus,
} = require('../../services/subscription');
const { sendPushNotification } = require('../../services/notification');
const { logger } = require('../../utils/logger');

class SubscriptionWebhookHandler extends WebhookHandler {
  constructor(config) {
    super(config);
    this.statusMap = {
      // Common statuses
      SUBSCRIPTION_PURCHASED: 'active',
      SUBSCRIPTION_RENEWED: 'active',
      SUBSCRIPTION_CANCELED: 'cancelled',
      SUBSCRIPTION_EXPIRED: 'expired',
      SUBSCRIPTION_ON_HOLD: 'on_hold',
      SUBSCRIPTION_IN_GRACE_PERIOD: 'grace_period',
      SUBSCRIPTION_RESTARTED: 'active',
      SUBSCRIPTION_REVOKED: 'revoked',
      SUBSCRIPTION_PAUSED: 'paused',
      SUBSCRIPTION_FAILED: 'failed',
      SUBSCRIPTION_STATUS_CHANGED: 'status_changed',
    };
  }

  async handleWebhook(payload) {
    try {
      // Validate webhook payload
      const { error, value } = validateWebhookPayload(payload);
      if (error) {
        logger.error('Invalid webhook payload:', error);
        return this.createErrorResponse('Invalid webhook payload', 400);
      }

      const { platform, event, data } = value;
      logger.info(`Processing ${platform} webhook event: ${event}`);

      // Extract subscription ID based on platform
      const subscriptionId = this.getSubscriptionId(data, platform);
      if (!subscriptionId) {
        logger.error('Could not find subscription ID in payload');
        return this.createErrorResponse('Invalid subscription ID', 400);
      }

      // Get current subscription status
      const currentStatus = await getSubscriptionStatus(subscriptionId);
      if (!currentStatus) {
        logger.error('Subscription not found:', subscriptionId);
        return this.createErrorResponse('Subscription not found', 404);
      }

      // Update subscription status based on event
      const updatedStatus = await this.updateStatus(currentStatus, event, data);
      if (!updatedStatus) {
        return this.createErrorResponse(
          'Failed to update subscription status',
          500,
        );
      }

      // Send push notification if needed
      if (this.shouldSendNotification(event)) {
        await this.sendNotification(platform, event, updatedStatus);
      }

      return this.createSuccessResponse('Webhook processed successfully', {
        subscriptionId,
        event,
        status: updatedStatus.status,
      });
    } catch (error) {
      logger.error('Error processing webhook:', error);
      return this.createErrorResponse('Internal server error', 500);
    }
  }

  getSubscriptionId(data, platform) {
    if (platform === 'android') {
      return data.subscriptionId || data.purchaseToken;
    } else if (platform === 'ios') {
      return data.subscriptionId || data.transactionId;
    }
    return null;
  }

  async updateStatus(currentStatus, event, data) {
    const newStatus = this.statusMap[event] || currentStatus.status;
    if (newStatus === currentStatus.status) {
      return currentStatus;
    }

    const updateData = {
      status: newStatus,
      last_updated: new Date(),
      metadata: {
        ...currentStatus.metadata,
        last_event: event,
        event_data: data,
        platform: data.platform,
      },
    };

    // Add platform-specific fields
    if (data.platform === 'android') {
      updateData.purchase_token = data.purchaseToken;
      updateData.expiry_date = data.expiryDate;
    } else if (data.platform === 'ios') {
      updateData.transaction_id = data.transactionId;
      updateData.expiry_date = data.expiryDate;
      updateData.auto_renew_status = data.autoRenewStatus;
    }

    return await updateSubscriptionStatus(currentStatus.id, updateData);
  }

  shouldSendNotification(event) {
    const notifyEvents = [
      'SUBSCRIPTION_PURCHASED',
      'SUBSCRIPTION_RENEWED',
      'SUBSCRIPTION_CANCELED',
      'SUBSCRIPTION_EXPIRED',
      'SUBSCRIPTION_ON_HOLD',
      'SUBSCRIPTION_IN_GRACE_PERIOD',
      'SUBSCRIPTION_FAILED',
    ];
    return notifyEvents.includes(event);
  }

  async sendNotification(platform, event, status) {
    const notificationMap = {
      SUBSCRIPTION_PURCHASED:
        'Your subscription has been activated successfully!',
      SUBSCRIPTION_RENEWED: 'Your subscription has been renewed successfully!',
      SUBSCRIPTION_CANCELED: 'Your subscription has been cancelled.',
      SUBSCRIPTION_EXPIRED: 'Your subscription has expired.',
      SUBSCRIPTION_ON_HOLD: 'Your subscription is on hold.',
      SUBSCRIPTION_IN_GRACE_PERIOD: 'Your subscription is in grace period.',
      SUBSCRIPTION_FAILED: 'There was an issue with your subscription renewal.',
    };

    const message = notificationMap[event];
    if (!message) return;

    await sendPushNotification({
      platform,
      token: status.device_token,
      title: 'Subscription Update',
      body: message,
      data: {
        type: 'subscription',
        status: status.status,
        event: event,
        subscriptionId: status.subscription_id,
      },
    });
  }

  createSuccessResponse(message, data = {}) {
    return {
      success: true,
      message,
      data,
    };
  }

  createErrorResponse(message, statusCode) {
    return {
      success: false,
      message,
      statusCode,
    };
  }
}

module.exports = SubscriptionWebhookHandler;
