class WebhookConfig {
  constructor(config) {
    this.validateConfig(config);
    this.dbConfig = config.dbConfig;
    this.webhookType = config.webhookType;
    this.platform = config.platform;
    this.notificationConfig = config.notificationConfig || {};
  }

  validateConfig(config) {
    if (!config.dbConfig) {
      throw new Error('Database configuration is required');
    }
    if (!config.webhookType) {
      throw new Error('Webhook type is required');
    }
    if (!config.platform) {
      throw new Error('Platform is required');
    }
  }

  getDbConfig() {
    return this.dbConfig;
  }

  getWebhookType() {
    return this.webhookType;
  }

  getPlatform() {
    return this.platform;
  }

  getNotificationConfig() {
    return this.notificationConfig;
  }
}

module.exports = { WebhookConfig };
