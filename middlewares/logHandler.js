const config = require('../config');
const { executeQuery } = require('../config/dbConfig');
const actionMap = require('../config/endpoints.json');
const jwt = require('jsonwebtoken');

const getActionType = (action) => {
  const basePath = action.split('/v2')[1]?.split('/')[1] || '';
  const endpoint =
    '/' + (action.split('/v2')[1]?.split('/').slice(2).join('/') || '');

  if (actionMap[`/${basePath}`]) {
    const match = actionMap[`/${basePath}`].find(
      (e) => e.endpoint === endpoint,
    );
    return match ? match.actionType : 'Other';
  }
  return 'Other';
};

const logUserActivity = async (req, res, next) => {
  try {
    const { devicename, latitude, longitude, apikey, version, ip, address } =
      req.headers;
    if (req.path.includes('adminApi') || req.path.includes('webSite')) {
      next();
    } else {
      const action = `${req.method} ${req.originalUrl}`;
      const actionType = getActionType(action);
      const payload = JSON.stringify(req.body || {});
      const bearerHeader = req.headers['authorization'];

      let user_id = null;
      if (bearerHeader) {
        try {
          // Split the header and get the token
          const bearer = bearerHeader.split(' ');
          const token = bearer[1];
          const decoded = jwt.verify(token, config.jwtAccessToken);
          user_id = decoded.id || null; // Get user ID from auth
        } catch (error) {
          console.error('Error verifying token:', error);
        }
      }

      // Intercept response to log response data
      const originalSend = res.send;

      res.send = async function (data) {
        try {
          const response =
            typeof data === 'object' ? JSON.stringify(data) : data;
          const status_code = res.statusCode;
          const status =
            status_code >= 200 && status_code < 300 ? 'success' : 'failed';
          const user_agent = req.headers['user-agent'] || '';
          const referrer_url = req.headers['referer'] || '';
          const metadata = JSON.stringify({ apikey, version, address });

          // Ensure content_id is retrieved from either request or response
          let content_id = req.body.id || null;

          try {
            const parsedData =
              typeof data === 'string' ? JSON.parse(data) : data;
            if (parsedData && parsedData.data && parsedData.data.id) {
              content_id = parsedData.data.id;
            }
          } catch (error) {
            console.error('Error parsing response data:', error);
          }
          const sql =
            'CALL InsertUserActivityLog(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';

          const values = [
            user_id,
            content_id,
            action,
            payload,
            response,
            status_code,
            status,
            user_agent,
            ip || req.ip,
            latitude || null,
            longitude || null,
            devicename || null,
            req.headers['sec-ch-ua-platform'] || null, // OS detection
            referrer_url,
            metadata,
            actionType,
          ];

          // Execute MySQL query safely
          await executeQuery(sql, values);
        } catch (err) {
          console.error('Error processing log:', err);
        }

        originalSend.call(this, data);
      };

      next();
    }
  } catch (error) {
    console.error('Error in logging middleware:', error);
    next(); // Continue request even if logging fails
  }
};

module.exports = logUserActivity;
