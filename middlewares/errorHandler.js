const { isCelebrateError } = require('celebrate');
const os = require('os');
const { executeQuery } = require('../config/dbConfig');

// eslint-disable-next-line no-unused-vars
const errorHandler = async (err, req, res, next) => {
  if (isCelebrateError(err)) {
    const cleanJoiMessage = (msg) => msg.replace(/"([^"]+)"/g, '$1').trim();

    let errorDetails = {};

    err.details.forEach((value, key) => {
      const messages = value.details.map((detail) =>
        cleanJoiMessage(detail.message),
      );
      errorDetails[key] = {
        message: messages.join(', '),
        keys: value.details.map((detail) => detail.context.key),
      };
    });

    const firstKey = Object.keys(errorDetails)[0];
    const dynamicMessage =
      errorDetails[firstKey]?.message || 'Validation error occurred';

    return res.status(400).json({
      status: false,
      message: dynamicMessage,
      data: errorDetails,
    });
  }

  // Handle other types of errors (optional)
  if (err.status) {
    console.log('Error details:', err);
    return res.status(400).json({
      status: false,
      message: err.message || 'An error occurred',
      data: err.data || {},
    });
  }

  // Default error response
  const requestBody = JSON.stringify(req.body);
  const truncatedRequestBody =
    requestBody.length > 500
      ? requestBody.substring(0, 500) + '...'
      : requestBody;

  const errorLog = {
    service_name: 'TechBuddy',
    environment: process.env.NODE_ENV || 'dev',
    error_level: 'ERROR',
    error_code: res.statusCode,
    error_message: err.message || 'An unexpected error occurred',
    error_context: {
      method: req.method,
      url: req.originalUrl,
      body: truncatedRequestBody,
      query: req.query,
    },
    http_method: req.method,
    endpoint: req.originalUrl,
    request_body: truncatedRequestBody,
    user_agent: req.headers['user-agent'],
    user_id: req?.userData?.id ? req.userData.id : null,
    ip_address: req.ip,
    server_hostname: os.hostname(),
    os_info: `${os.platform()} ${os.release()}`,
    process_memory_mb: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
  };

  // Add default values for missing fields
  errorLog.service_name = errorLog.service_name || 'TechBuddy';
  errorLog.error_level = errorLog.error_level || 'ERROR';
  errorLog.error_code = errorLog.error_code || 500;
  errorLog.error_message =
    errorLog.error_message || 'An unexpected error occurred';
  errorLog.http_method = errorLog.http_method || 'GET';
  errorLog.endpoint = errorLog.endpoint || '/';
  errorLog.request_body = errorLog.request_body || '';
  errorLog.user_agent = errorLog.user_agent || 'Unknown';
  errorLog.user_id = errorLog.user_id || null;
  errorLog.environment = errorLog.environment || process.env.NODE_ENV || 'dev';
  errorLog.ip_address = errorLog.ip_address || '127.0.0.1';
  errorLog.server_hostname = errorLog.server_hostname || os.hostname();
  errorLog.os_info = errorLog.os_info || `${os.platform()} ${os.release()}`;
  errorLog.process_memory_mb = errorLog.process_memory_mb || 0;

  console.error('Error Log:', errorLog);
  await executeQuery('CALL insertErrorLog(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)', [
    errorLog.service_name,
    errorLog.error_level,
    errorLog.error_code,
    errorLog.error_message,
    JSON.stringify(errorLog.error_context),
    errorLog.http_method,
    errorLog.endpoint,
    errorLog.request_body,
    errorLog.user_agent,
    errorLog.user_id,
    errorLog.environment,
    errorLog.ip_address,
    errorLog.server_hostname,
    errorLog.os_info,
    errorLog.process_memory_mb,
  ]);

  return res.status(500).json({
    status: false,
    message:
      "We've logged this issue, and our team is working to resolve it as quickly as possible. We apologize for the inconvenience and appreciate your patience.",
    data: {},
  });
};

module.exports = errorHandler;
