{"openapi": "3.0.0", "info": {"title": "Express API", "version": "1.0.0", "description": "API documentation"}, "tags": [{"name": "Profile", "description": "User profile management"}], "paths": {"/api/sign-up": {"post": {"summary": "Register a new user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "social_id": {"type": "string", "example": "123456789"}, "social_type": {"type": "string", "example": "facebook"}, "device_name": {"type": "string", "example": "iPhone"}, "latitude": {"type": "string", "example": "37.7749"}, "longitude": {"type": "string", "example": "-122.4194"}, "social_profile_pic": {"type": "string", "example": "https://example.com/profile.jpg"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "string", "example": "example data"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid parameter"}}}}}}}}}, "/api/profile-setup": {"post": {"tags": ["Profile"], "summary": "Setup user profile", "description": "This endpoint allows users to set up their profile by providing necessary details.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["full_name", "email", "phone", "company", "designation", "work_experience", "dob", "gender", "about", "country_code"], "properties": {"full_name": {"type": "string", "description": "The full name of the user.", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "description": "The email address of the user.", "example": "<EMAIL>"}, "phone": {"type": "string", "description": "The phone number of the user (digits only).", "example": "1234567890"}, "company": {"type": "string", "description": "The name of the company the user works for.", "example": "ABC Corp"}, "designation": {"type": "string", "description": "The user's job title or designation.", "example": "Software Engineer"}, "work_experience": {"type": "string", "description": "A brief description of the user's work experience.", "example": "5 years in software development"}, "dob": {"type": "string", "format": "date", "description": "The date of birth of the user.", "example": "1990-01-01"}, "gender": {"type": "string", "description": "The gender of the user.", "example": "Male"}, "about": {"type": "string", "description": "A brief description about the user.", "example": "Passionate about technology and innovation."}, "country_code": {"type": "string", "description": "The country code associated with the user's phone number (digits only).", "example": "91"}}}}}}, "responses": {"200": {"description": "Profile setup successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Success message.", "example": "Profile setup successful."}, "data": {"type": "object", "description": "Details of the user profile.", "properties": {"user_id": {"type": "integer", "example": 1}, "full_name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "1234567890"}, "company": {"type": "string", "example": "ABC Corp"}, "designation": {"type": "string", "example": "Software Engineer"}, "work_experience": {"type": "string", "example": "5 years in software development"}, "dob": {"type": "string", "example": "1990-01-01"}, "gender": {"type": "string", "example": "Male"}, "about": {"type": "string", "example": "Passionate about technology and innovation."}, "country_code": {"type": "string", "example": "91"}}}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message.", "example": "Invalid input data"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message.", "example": "An unexpected error occurred"}}}}}}}}}}}