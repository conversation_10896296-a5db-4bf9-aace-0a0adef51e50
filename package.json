{"name": "techbuddy", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install", "start": "node index.js", "dev": "nodemon index.js", "lint": "eslint .", "format": "prettier --write ."}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-rekognition": "^3.840.0", "@aws-sdk/client-s3": "^3.844.0", "@sendgrid/mail": "^8.1.5", "agora-token": "^2.0.3", "algoliasearch": "^4.17.2", "apn": "^2.2.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "celebrate": "^15.0.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-jwt": "^8.4.1", "firebase-admin": "^13.2.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mysql2": "^3.11.0", "nodemailer": "^6.9.15", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.4.3", "uuid": "^10.0.0"}, "devDependencies": {"eslint": "^8.57.0", "husky": "^8.0.0", "lint-staged": "^6.1.1", "nodemon": "^3.1.4", "prettier": "^3.0.0"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}}