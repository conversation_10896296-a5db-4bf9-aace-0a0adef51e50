const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} = require('@aws-sdk/client-s3');
const {
  awsAccessKey,
  awsRegion,
  awsSecretKey,
  awsBucketName,
  awsCdnPath,
} = require('../config/index');

// Configure S3 client
const s3Client = new S3Client({
  region: awsRegion,
  credentials: {
    accessKeyId: awsAccessKey,
    secretAccessKey: awsSecretKey,
  },
});

class SimpleVideoProcessor {
  constructor() {
    this.tempDir = path.join(__dirname, '../temp');
    this.ensureTempDir();
  }

  ensureTempDir() {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true, mode: 0o755 });
    }
  }

  /**
   * Download video from S3 using direct command
   */
  async downloadFromS3(s3Key) {
    try {
      console.log('📥 Downloading video from S3:', s3Key);

      const command = new GetObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
      });

      const response = await s3Client.send(command);
      const tempInputPath = path.join(
        this.tempDir,
        `input_${Date.now()}_${path.basename(s3Key)}`,
      );

      // Convert stream to buffer
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      // Write to file
      fs.writeFileSync(tempInputPath, buffer);

      console.log('✅ Video downloaded successfully:', tempInputPath);
      return tempInputPath;
    } catch (error) {
      console.error('❌ Error downloading video from S3:', error);
      throw error;
    }
  }

  /**
   * Re-encode video using direct FFmpeg command
   */
  async reencodeVideo(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log('🔧 Re-encoding video with direct FFmpeg...');

      const ffmpegArgs = [
        '-i',
        inputPath,
        '-c:v',
        'libx264',
        '-profile:v',
        'main', // Changed from baseline to main for better compatibility
        '-level',
        '4.0', // Increased level for better support
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-profile:a',
        'aac_low', // Use AAC-LC profile (corresponds to audio/mp4a-latm)
        '-aac_coder',
        'twoloop', // Better AAC encoder
        '-b:a',
        '128k',
        '-ar',
        '44100', // Set audio sample rate
        '-ac',
        '2', // Force stereo
        '-af',
        'aresample=async=1', // Audio resampling filter for compatibility
        '-movflags',
        '+faststart', // Enables progressive download
        '-preset',
        'medium', // Better quality than 'fast'
        '-crf',
        '23', // Constant rate factor for good quality
        '-maxrate',
        '2M', // Maximum bitrate
        '-bufsize',
        '4M', // Buffer size
        '-avoid_negative_ts',
        'make_zero',
        '-fflags',
        '+genpts',
        '-map_metadata',
        '-1', // Remove metadata that might cause issues
        '-y', // Overwrite output file
        outputPath,
      ];

      console.log('🎬 FFmpeg command:', 'ffmpeg', ffmpegArgs.join(' '));

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
        // Log progress
        if (data.toString().includes('time=')) {
          const timeMatch = data.toString().match(/time=(\d+:\d+:\d+\.\d+)/);
          if (timeMatch) {
            console.log(`⏳ Processing time: ${timeMatch[1]}`);
          }
        }
      });

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          if (fs.existsSync(outputPath)) {
            console.log('✅ Video re-encoding completed successfully');
            resolve(outputPath);
          } else {
            reject(new Error('Output file was not created'));
          }
        } else {
          console.error('❌ FFmpeg stderr:', stderr);

          // If audio codec failed, try with audio stream copy first
          if (
            stderr.includes('Audio object type') ||
            stderr.includes('Function not implemented') ||
            stderr.includes('Could not find codec parameters for stream') ||
            stderr.includes('Error while opening decoder') ||
            stderr.includes('Error initializing a simple filtergraph') ||
            stderr.includes('is not implemented')
          ) {
            console.log(
              '🔄 Retrying with audio stream copy due to codec issues...',
            );
            this.reencodeVideoWithAudioCopy(inputPath, outputPath)
              .then(resolve)
              .catch(() => {
                console.log(
                  '🔄 Audio copy failed, trying with audio extraction and re-encoding...',
                );
                this.reencodeVideoWithAudioExtraction(inputPath, outputPath)
                  .then(resolve)
                  .catch(() => {
                    console.log(
                      '🔄 Audio extraction failed, trying with forced audio conversion...',
                    );
                    this.reencodeVideoWithForcedAudio(inputPath, outputPath)
                      .then(resolve)
                      .catch(() => {
                        console.log(
                          '🔄 Audio conversion failed, trying without audio as final fallback...',
                        );
                        this.reencodeVideoNoAudio(inputPath, outputPath)
                          .then(resolve)
                          .catch(reject);
                      });
                  });
              });
          } else {
            reject(new Error(`FFmpeg exited with code ${code}: ${stderr}`));
          }
        }
      });

      ffmpeg.on('error', (error) => {
        console.error('❌ FFmpeg spawn error:', error);
        reject(error);
      });
    });
  }

  /**
   * Re-encode video with forced audio conversion (for problematic audio codecs)
   */
  async reencodeVideoWithForcedAudio(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log('🔧 Re-encoding video with forced audio conversion...');

      const ffmpegArgs = [
        '-i',
        inputPath,
        '-c:v',
        'libx264',
        '-profile:v',
        'main',
        '-level',
        '4.0',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-profile:a',
        'aac_low', // Force AAC-LC profile
        '-b:a',
        '128k',
        '-ar',
        '44100',
        '-ac',
        '2',
        '-af',
        'aresample=async=1:first_pts=0', // More aggressive audio resampling
        '-vf',
        'scale=trunc(iw/2)*2:trunc(ih/2)*2', // Ensure even dimensions
        '-movflags',
        '+faststart',
        '-preset',
        'medium',
        '-crf',
        '23',
        '-maxrate',
        '2M',
        '-bufsize',
        '4M',
        '-avoid_negative_ts',
        'make_zero',
        '-fflags',
        '+genpts',
        '-map_metadata',
        '-1',
        '-map',
        '0:v:0', // Explicitly map first video stream
        '-map',
        '0:a:0?', // Map first audio stream if it exists (? makes it optional)
        '-y',
        outputPath,
      ];

      console.log(
        '🎬 FFmpeg command (forced audio):',
        'ffmpeg',
        ffmpegArgs.join(' '),
      );

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          console.log(
            '✅ Video re-encoded successfully with forced audio conversion',
          );
          resolve(outputPath);
        } else {
          console.error('❌ FFmpeg stderr (forced audio):', stderr);
          reject(new Error(`FFmpeg exited with code ${code}: ${stderr}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg spawn error: ${error.message}`));
      });
    });
  }

  /**
   * Re-encode video with audio stream copy (for unsupported audio codecs)
   */
  async reencodeVideoWithAudioCopy(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log('🔧 Re-encoding video with audio stream copy...');

      const ffmpegArgs = [
        '-i',
        inputPath,
        '-c:v',
        'libx264',
        '-profile:v',
        'main',
        '-level',
        '4.0',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'copy', // Copy audio stream without re-encoding
        '-vf',
        'scale=trunc(iw/2)*2:trunc(ih/2)*2', // Ensure even dimensions
        '-movflags',
        '+faststart',
        '-preset',
        'medium',
        '-crf',
        '23',
        '-maxrate',
        '2M',
        '-bufsize',
        '4M',
        '-avoid_negative_ts',
        'make_zero',
        '-fflags',
        '+genpts',
        '-map_metadata',
        '-1',
        '-map',
        '0:v:0', // Explicitly map first video stream
        '-map',
        '0:a:0?', // Map first audio stream if it exists (? makes it optional)
        '-y',
        outputPath,
      ];

      console.log(
        '🎬 FFmpeg command (audio copy):',
        'ffmpeg',
        ffmpegArgs.join(' '),
      );

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          console.log(
            '✅ Video re-encoded successfully with audio stream copy',
          );
          resolve(outputPath);
        } else {
          console.error('❌ FFmpeg stderr (audio copy):', stderr);
          reject(new Error(`FFmpeg exited with code ${code}: ${stderr}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg spawn error: ${error.message}`));
      });
    });
  }

  /**
   * Re-encode video with audio extraction (for problematic AAC streams)
   */
  async reencodeVideoWithAudioExtraction(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log(
        '🔧 Re-encoding video with audio extraction and conversion...',
      );

      const ffmpegArgs = [
        '-i',
        inputPath,
        '-c:v',
        'libx264',
        '-profile:v',
        'main',
        '-level',
        '4.0',
        '-pix_fmt',
        'yuv420p',
        '-c:a',
        'aac',
        '-profile:a',
        'aac_low',
        '-b:a',
        '128k',
        '-ar',
        '44100',
        '-ac',
        '2',
        '-af',
        'aresample=async=1:first_pts=0,aformat=sample_fmts=fltp:sample_rates=44100:channel_layouts=stereo', // More aggressive audio processing
        '-vf',
        'scale=trunc(iw/2)*2:trunc(ih/2)*2',
        '-movflags',
        '+faststart',
        '-preset',
        'medium',
        '-crf',
        '23',
        '-maxrate',
        '2M',
        '-bufsize',
        '4M',
        '-avoid_negative_ts',
        'make_zero',
        '-fflags',
        '+genpts+igndts', // Ignore DTS issues
        '-map_metadata',
        '-1',
        '-map',
        '0:v:0',
        '-map',
        '0:a:0?',
        '-strict',
        'experimental', // Allow experimental features
        '-err_detect',
        'ignore_err', // Ignore errors in input stream
        '-y',
        outputPath,
      ];

      console.log(
        '🎬 FFmpeg command (audio extraction):',
        'ffmpeg',
        ffmpegArgs.join(' '),
      );

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          console.log('✅ Video re-encoded successfully with audio extraction');
          resolve(outputPath);
        } else {
          console.error('❌ FFmpeg stderr (audio extraction):', stderr);
          reject(new Error(`FFmpeg exited with code ${code}: ${stderr}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg spawn error: ${error.message}`));
      });
    });
  }

  /**
   * Re-encode video without audio (fallback for audio codec issues)
   */
  async reencodeVideoNoAudio(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log('🔧 Re-encoding video without audio (fallback)...');

      const ffmpegArgs = [
        '-i',
        inputPath,
        '-c:v',
        'libx264',
        '-profile:v',
        'main', // Changed from baseline to main
        '-level',
        '4.0', // Increased level
        '-pix_fmt',
        'yuv420p',
        '-an', // No audio
        '-movflags',
        '+faststart',
        '-preset',
        'medium', // Better quality
        '-crf',
        '23', // Constant rate factor
        '-maxrate',
        '2M', // Maximum bitrate
        '-bufsize',
        '4M', // Buffer size
        '-avoid_negative_ts',
        'make_zero',
        '-fflags',
        '+genpts',
        '-map_metadata',
        '-1', // Remove metadata
        '-y', // Overwrite output file
        outputPath,
      ];

      console.log(
        '🎬 FFmpeg command (no audio):',
        'ffmpeg',
        ffmpegArgs.join(' '),
      );

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
        if (data.toString().includes('time=')) {
          const timeMatch = data.toString().match(/time=(\d+:\d+:\d+\.\d+)/);
          if (timeMatch) {
            console.log(`⏳ Processing time: ${timeMatch[1]}`);
          }
        }
      });

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          if (fs.existsSync(outputPath)) {
            console.log(
              '✅ Video re-encoding completed successfully (no audio)',
            );
            resolve(outputPath);
          } else {
            reject(new Error('Output file was not created'));
          }
        } else {
          console.error('❌ FFmpeg stderr (no audio):', stderr);
          reject(new Error(`FFmpeg exited with code ${code}: ${stderr}`));
        }
      });

      ffmpeg.on('error', (error) => {
        console.error('❌ FFmpeg spawn error (no audio):', error);
        reject(error);
      });
    });
  }

  /**
   * Upload processed video back to S3
   */
  async uploadToS3(filePath, s3Key) {
    try {
      console.log('📤 Uploading processed video to S3:', s3Key);

      const fileBuffer = fs.readFileSync(filePath);
      const command = new PutObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
        Body: fileBuffer,
        ContentType: 'video/mp4',
        ACL: 'public-read', // Make video publicly accessible
        CacheControl: 'max-age=31536000', // Cache for 1 year
        ContentDisposition: 'inline', // Allow inline viewing
        Metadata: {
          'processed-for': 'aws-rekognition',
          'processed-at': new Date().toISOString(),
          'original-size': fileBuffer.length.toString(),
          codec: 'h264',
          container: 'mp4',
        },
      });

      await s3Client.send(command);
      console.log('✅ Video uploaded successfully to S3 (replaced original)');

      // Verify the uploaded file
      await this.verifyUploadedVideo(s3Key);
    } catch (error) {
      console.error('❌ Error uploading video to S3:', error);
      throw error;
    }
  }

  /**
   * Verify processed video file integrity
   */
  async verifyProcessedVideo(filePath) {
    return new Promise((resolve, reject) => {
      console.log('🔍 Verifying processed video integrity:', filePath);

      const ffprobe = spawn('ffprobe', [
        '-v',
        'quiet',
        '-print_format',
        'json',
        '-show_format',
        '-show_streams',
        filePath,
      ]);

      let stdout = '';
      let stderr = '';

      ffprobe.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ffprobe.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffprobe.on('close', (code) => {
        if (code === 0) {
          try {
            const info = JSON.parse(stdout);
            const videoStream = info.streams.find(
              (s) => s.codec_type === 'video',
            );
            const audioStream = info.streams.find(
              (s) => s.codec_type === 'audio',
            );

            console.log('✅ Video integrity check passed:', {
              duration: info.format.duration,
              size: info.format.size,
              videoCodec: videoStream?.codec_name,
              audioCodec: audioStream?.codec_name,
              width: videoStream?.width,
              height: videoStream?.height,
            });

            // Basic validation
            if (!videoStream) {
              throw new Error('No video stream found');
            }

            if (videoStream.codec_name !== 'h264') {
              console.warn(
                '⚠️ Video codec is not H.264:',
                videoStream.codec_name,
              );
            }

            resolve(true);
          } catch (parseError) {
            reject(
              new Error(`Failed to parse video info: ${parseError.message}`),
            );
          }
        } else {
          reject(new Error(`FFprobe failed with code ${code}: ${stderr}`));
        }
      });

      ffprobe.on('error', (error) => {
        reject(new Error(`FFprobe spawn error: ${error.message}`));
      });
    });
  }

  /**
   * Verify uploaded video is accessible and has correct headers
   */
  async verifyUploadedVideo(s3Key) {
    try {
      console.log('🔍 Verifying uploaded video:', s3Key);

      const command = new GetObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
      });

      const response = await s3Client.send(command);

      console.log('✅ Video verification successful:', {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        lastModified: response.LastModified,
        cacheControl: response.CacheControl,
        contentDisposition: response.ContentDisposition,
      });

      return true;
    } catch (error) {
      console.error('❌ Video verification failed:', error);
      throw new Error(`Video verification failed: ${error.message}`);
    }
  }

  /**
   * Clean up temporary files
   */
  cleanup(filePaths) {
    filePaths.forEach((filePath) => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('🗑️ Cleaned up temp file:', filePath);
      }
    });
  }

  /**
   * Process video: download, re-encode, upload (replace original)
   */
  async processVideo(originalS3Url) {
    const tempFiles = [];

    try {
      const s3Key = this.extractS3Key(originalS3Url);
      console.log('🎥 Processing video:', s3Key);

      // Use same S3 key to replace the original file
      const processedS3Key = s3Key;

      // Download original video
      const inputPath = await this.downloadFromS3(s3Key);
      tempFiles.push(inputPath);

      // Re-encode video
      const outputPath = path.join(this.tempDir, `processed_${Date.now()}.mp4`);
      tempFiles.push(outputPath);

      await this.reencodeVideo(inputPath, outputPath);

      // Verify the processed video before uploading
      await this.verifyProcessedVideo(outputPath);

      // Upload processed video to replace the original
      await this.uploadToS3(outputPath, processedS3Key);

      console.log('🎉 Video processing completed successfully!');
      console.log(
        '📍 Original video replaced with processed version at:',
        s3Key,
      );

      return {
        success: true,
        originalUrl: originalS3Url,
        processedUrl: originalS3Url, // Same URL since we replaced the original
        processedS3Key: processedS3Key,
      };
    } catch (error) {
      console.error('💥 Video processing failed:', error);
      throw error;
    } finally {
      this.cleanup(tempFiles);
    }
  }

  extractS3Key(s3Url) {
    try {
      const urlParts = new URL(s3Url);
      const path = urlParts.pathname;
      return path.startsWith('/') ? path.substring(1) : path;
    } catch (error) {
      throw new Error('Invalid S3 URL format');
    }
  }

  // Removed generateProcessedKey - we now replace the original file

  generateS3Url(s3Key) {
    // Use CDN URL if available, otherwise use direct S3 URL
    if (awsCdnPath) {
      return `${awsCdnPath}/${s3Key}`;
    }
    return `https://${awsBucketName}.s3.${awsRegion}.amazonaws.com/${s3Key}`;
  }

  async needsProcessing(s3Url) {
    try {
      const s3Key = this.extractS3Key(s3Url);

      // Check if file has already been processed by checking metadata
      const command = new GetObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
      });

      try {
        const response = await s3Client.send(command);
        // Check if metadata indicates it's already processed
        const isProcessed =
          response.Metadata &&
          response.Metadata['processed-for'] === 'aws-rekognition';
        return !isProcessed;
      } catch (error) {
        // If we can't check metadata, assume it needs processing
        return true;
      }
    } catch (error) {
      return true;
    }
  }
}

module.exports = new SimpleVideoProcessor();
