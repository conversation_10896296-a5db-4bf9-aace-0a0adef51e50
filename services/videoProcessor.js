const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');
const path = require('path');
const {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} = require('@aws-sdk/client-s3');
const {
  awsAccessKey,
  awsRegion,
  awsSecretKey,
  awsBucketName,
} = require('../config/index');

// Check if FFmpeg is available
try {
  require('child_process').execSync('ffmpeg -version', { stdio: 'ignore' });
} catch (error) {
  console.warn('⚠️ FFmpeg not found. Video processing will be disabled.');
  console.warn('💡 Install FFmpeg: sudo apt update && sudo apt install ffmpeg');
}

// Configure S3 client
const s3Client = new S3Client({
  region: awsRegion,
  credentials: {
    accessKeyId: awsAccessKey,
    secretAccessKey: awsSecretKey,
  },
});

class VideoProcessor {
  constructor() {
    this.tempDir = path.join(__dirname, '../temp');
    this.ensureTempDir();
  }

  ensureTempDir() {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * Download video from S3 to local temp file
   */
  async downloadFromS3(s3Key) {
    try {
      console.log('📥 Downloading video from S3:', s3Key);

      const command = new GetObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
      });

      const response = await s3Client.send(command);
      const tempInputPath = path.join(
        this.tempDir,
        `input_${Date.now()}_${path.basename(s3Key)}`,
      );

      // Convert stream to buffer and write to file
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      // Write buffer to file with proper permissions
      fs.writeFileSync(tempInputPath, buffer, { mode: 0o644 });

      console.log('✅ Video downloaded successfully:', tempInputPath);
      console.log('📊 File size:', buffer.length, 'bytes');

      return tempInputPath;
    } catch (error) {
      console.error('❌ Error downloading video from S3:', error);
      throw error;
    }
  }

  /**
   * Re-encode video for AWS Rekognition compatibility
   */
  async reencodeVideo(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
      console.log('🔧 Re-encoding video for AWS Rekognition compatibility...');

      // Ensure output directory exists and has proper permissions
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true, mode: 0o755 });
      }

      // Remove output file if it exists
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }

      ffmpeg(inputPath)
        .videoCodec('libx264')
        .audioCodec('aac')
        .videoBitrate('1000k')
        .audioBitrate('128k')
        .outputOptions([
          '-profile:v baseline',
          '-level 3.0',
          '-pix_fmt yuv420p',
          '-movflags +faststart',
          '-preset fast',
          '-crf 23',
          '-avoid_negative_ts make_zero',
          '-fflags +genpts',
        ])
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('🎬 FFmpeg command:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(
            `⏳ Processing: ${Math.round(progress.percent || 0)}% done`,
          );
        })
        .on('end', () => {
          // Verify output file was created
          if (fs.existsSync(outputPath)) {
            console.log('✅ Video re-encoding completed successfully');
            resolve(outputPath);
          } else {
            reject(new Error('Output file was not created'));
          }
        })
        .on('error', (error) => {
          console.error('❌ FFmpeg error:', error);
          reject(error);
        })
        .run();
    });
  }

  /**
   * Upload processed video back to S3
   */
  async uploadToS3(filePath, s3Key) {
    try {
      console.log('📤 Uploading processed video to S3:', s3Key);

      const fileStream = fs.createReadStream(filePath);
      const command = new PutObjectCommand({
        Bucket: awsBucketName,
        Key: s3Key,
        Body: fileStream,
        ContentType: 'video/mp4',
        Metadata: {
          'processed-for': 'aws-rekognition',
          'processed-at': new Date().toISOString(),
        },
      });

      const response = await s3Client.send(command);
      console.log('✅ Video uploaded successfully to S3');
      return response;
    } catch (error) {
      console.error('❌ Error uploading video to S3:', error);
      throw error;
    }
  }

  /**
   * Clean up temporary files
   */
  cleanup(filePaths) {
    filePaths.forEach((filePath) => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('🗑️ Cleaned up temp file:', filePath);
      }
    });
  }

  /**
   * Process video: download, re-encode, upload
   */
  async processVideo(originalS3Url) {
    const tempFiles = [];

    try {
      // Extract S3 key from URL
      const s3Key = this.extractS3Key(originalS3Url);
      console.log('🎥 Processing video:', s3Key);

      // Generate processed file key
      const processedS3Key = this.generateProcessedKey(s3Key);

      // Download original video
      const inputPath = await this.downloadFromS3(s3Key);
      tempFiles.push(inputPath);

      // Re-encode video
      const outputPath = path.join(this.tempDir, `processed_${Date.now()}.mp4`);
      tempFiles.push(outputPath);

      await this.reencodeVideo(inputPath, outputPath);

      // Upload processed video
      await this.uploadToS3(outputPath, processedS3Key);

      // Generate new S3 URL
      const processedUrl = this.generateS3Url(processedS3Key);

      console.log('🎉 Video processing completed successfully!');
      console.log('📍 Original URL:', originalS3Url);
      console.log('📍 Processed URL:', processedUrl);

      return {
        success: true,
        originalUrl: originalS3Url,
        processedUrl: processedUrl,
        processedS3Key: processedS3Key,
      };
    } catch (error) {
      console.error('💥 Video processing failed:', error);
      throw error;
    } finally {
      // Clean up temp files
      this.cleanup(tempFiles);
    }
  }

  /**
   * Extract S3 key from URL
   */
  extractS3Key(s3Url) {
    try {
      const urlParts = new URL(s3Url);
      const path = urlParts.pathname;
      return path.startsWith('/') ? path.substring(1) : path;
    } catch (error) {
      throw new Error('Invalid S3 URL format');
    }
  }

  /**
   * Generate processed file key
   */
  generateProcessedKey(originalKey) {
    const parsedPath = path.parse(originalKey);
    return path
      .join(parsedPath.dir, `${parsedPath.name}_processed${parsedPath.ext}`)
      .replace(/\\/g, '/'); // Ensure forward slashes for S3
  }

  /**
   * Generate S3 URL from key
   */
  generateS3Url(s3Key) {
    // You might want to use CloudFront URL instead
    return `https://${awsBucketName}.s3.${awsRegion}.amazonaws.com/${s3Key}`;
  }

  /**
   * Check if video needs processing
   */
  async needsProcessing(s3Url) {
    try {
      const s3Key = this.extractS3Key(s3Url);

      // Check if already processed
      if (s3Key.includes('_processed')) {
        return false;
      }

      // You could add more sophisticated checks here
      // like checking video metadata, codec, etc.
      return true;
    } catch (error) {
      console.error('Error checking if video needs processing:', error);
      return true; // Default to processing if unsure
    }
  }
}

module.exports = new VideoProcessor();
