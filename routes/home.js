const homeSchema = require('../validators/home');

const express = require('express');
const { verifyToken } = require('../utils/jwtUtils');
const { celebrate } = require('celebrate');
const router = express.Router();
const home = require('../controllers/home');

router.post(
  '/like-user',
  verifyToken,
  celebrate({ body: homeSchema.likeSchema }),
  home.likeUser,
);
router.post(
  '/show-user-likes',
  verifyToken,
  celebrate({ body: homeSchema.likelistSchema }),
  home.showUserLikes,
);

router.post('/show-user-match', verifyToken, home.matchUser);

router.post('/show-user-matches', verifyToken, home.matchUser);

router.post(
  '/settings-config',
  verifyToken,
  celebrate({
    body: homeSchema.settingsConfigSchema,
  }),
  home.updateSettings,
);

router.post(
  '/near-by',
  verifyToken,
  celebrate({
    body: homeSchema.nearBySchema,
  }),
  home.nearBy,
);

router.post(
  '/best-matches',
  verifyToken,
  celebrate({ body: homeSchema.MatchSchema }),
  home.bestMatches,
);
router.post(
  '/best-map-matches',
  verifyToken,
  celebrate({ body: homeSchema.MapSchema }),
  home.bestMatchesWithMap,
);

router.post(
  '/top-users',
  verifyToken,
  celebrate({ body: homeSchema.topUsers }),
  home.topUsers,
);
router.post(
  '/rate-users',
  verifyToken,
  celebrate({ body: homeSchema.rateUser }),

  home.rateUser,
);
router.get(
  '/get-subscription-home',
  verifyToken,
  home.GetSubscriptionDetailForHome,
);

router.post(
  '/get-purpose-and-status-of-message',
  verifyToken,
  celebrate({ body: homeSchema.purposeAndStatusOfMessage }),
  home.getPurposeAndStatusOfMessage,
);

module.exports = router;
