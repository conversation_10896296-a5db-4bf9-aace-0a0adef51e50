const express = require('express');
const router = express.Router();
const educationData = require('../../controllers/Admin/education');
const educationSchema = require('../../validators/AdminValidator/education');
const { celebrate } = require('celebrate');
const { verifyAdminToken } = require('../../utils/jwtUtils');

router.post(
  '/addInstitutes',
  verifyAdminToken,
  celebrate({
    body: educationSchema.institudeSchema,
  }),
  educationData.addInstitude,
);
router.post(
  '/addDegree',
  verifyAdminToken,
  celebrate({
    body: educationSchema.degreeSchema,
  }),
  educationData.addDegree,
);
router.get(
  '/getDegreeList',
  verifyAdminToken,

  educationData.getDegreelist,
);
router.get(
  '/getAllFieldOfStudy',
  verifyAdminToken,

  educationData.getFieldList,
);
router.post(
  '/getDegreeListWithPage',
  verifyAdminToken,

  educationData.getDegreelistWithPagination,
);
router.post(
  '/getFieldOfStudy',
  verifyAdminToken,

  educationData.getFieldOfStudy,
);
router.post(
  '/getInstitudeslist',
  verifyAdminToken,

  educationData.GetInstitudes,
);
router.post(
  '/addFieldOfStudy',
  verifyAdminToken,
  celebrate({
    body: educationSchema.fieldOfStudySchema,
  }),
  educationData.addfieldOfStudy,
);

router.post('/get-records', verifyAdminToken, educationData.GetTotalRecords);
router.post('/edit-education-data', educationData.editEducationData);

router.post('/addMasterDegree', educationData.addDegreeAsMasterEntry);
router.post('/AssignMasterDegree', educationData.assignDegreeAsMasterEntry);

router.post('/addMasterFeilds', educationData.addFeildsAsMasterEntry);
router.post('/AssignMasterFields', educationData.assignFieldsAsMasterEntry);

router.post('/addMasterInstitudes', educationData.addInstitudesAsMasterEntry);
router.post(
  '/AssignMasterInstitudes',
  educationData.assignInstitudesAsMasterEntry,
);
router.get('/get-institudes', verifyAdminToken, educationData.getInstitude);

module.exports = router;
