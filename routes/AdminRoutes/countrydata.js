const express = require('express');
const router = express.Router();
const countryData = require('../../controllers/Admin/countrydata');
const countrySchema = require('../../validators/AdminValidator/countrydata');
const { celebrate } = require('celebrate');
const { verifyAdminToken } = require('../../utils/jwtUtils');

router.post(
  '/addCountry',
  verifyAdminToken,
  celebrate({
    body: countrySchema.countrySchema,
  }),
  countryData.addCountries,
);
router.post(
  '/addState',
  verifyAdminToken,
  celebrate({
    body: countrySchema.stateSchema,
  }),
  countryData.addState,
);
router.post(
  '/addCity',
  verifyAdminToken,
  celebrate({
    body: countrySchema.citySchema,
  }),
  countryData.addCity,
);
router.post(
  '/listCountries',
  verifyAdminToken,
  // celebrate({
  //   body: countrySchema.countrySchema,
  // }),
  countryData.GetCountries,
);
router.post(
  '/listStates',
  verifyAdminToken,
  // celebrate({
  //   body: countrySchema.countrySchema,
  // }),
  countryData.GetStates,
);
router.post(
  '/listCity',
  verifyAdminToken,
  // celebrate({
  //   body: countrySchema.countrySchema,
  // }),
  countryData.GetCity,
);
router.post('/get-records', verifyAdminToken, countryData.GetTotalRecords);

router.post('/list-countries', verifyAdminToken, countryData.GetCountryList);
router.post('/list-state', verifyAdminToken, countryData.GetStateList);

module.exports = router;
