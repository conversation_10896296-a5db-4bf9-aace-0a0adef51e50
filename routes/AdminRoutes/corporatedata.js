const express = require('express');
const router = express.Router();
const corporateData = require('../../controllers/Admin/corporatedata');
const corporateSchema = require('../../validators/AdminValidator/corporatedata');
const { celebrate } = require('celebrate');
const { verifyAdminToken } = require('../../utils/jwtUtils');

router.post(
  '/addCompanies',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.companeySchema,
  }),
  corporateData.addCompanies,
);
router.post(
  '/getCompanies',
  verifyAdminToken,

  corporateData.GetCompanies,
);
router.post(
  '/getDesignation',
  verifyAdminToken,

  corporateData.GetDesignation,
);
router.get('/getMasterSkill', verifyAdminToken, corporateData.GetMasterSkill);
router.post(
  '/getMasterSkillforAdmin',
  verifyAdminToken,
  corporateData.GetMasterSkillWithpagination,
);
router.post(
  '/getSubSkillforAdmin',
  verifyAdminToken,
  corporateData.GetSubSkillWithpagination,
);

router.post(
  '/get-experiance-records',
  verifyAdminToken,
  corporateData.GetTotalRecords,
);

router.post(
  '/get-skills-records',
  verifyAdminToken,
  corporateData.GetSkillTotalRecords,
);

router.post(
  '/addMasterSkill',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.masterSkillSchema,
  }),
  corporateData.addMasterSkill,
);
router.post(
  '/addSubSkill',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.subSkillSchema,
  }),
  corporateData.addSubSkill,
);
router.post(
  '/addInterest',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.interestSchema,
  }),
  corporateData.addInterest,
);
router.post(
  '/addInterest',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.interestSchema,
  }),
  corporateData.addInterest,
);
router.post(
  '/addDesignation',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.designationSchema,
  }),
  corporateData.addDesignation,
);
router.post(
  '/addLanguages',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.languageSchema,
  }),
  corporateData.addLanguage,
);
router.post(
  '/addJobPositions',
  verifyAdminToken,
  celebrate({
    body: corporateSchema.positionSchema,
  }),
  corporateData.addPosition,
);
router.post('/getInterest', verifyAdminToken, corporateData.getInterest);
router.post('/getLanguages', verifyAdminToken, corporateData.getlanguages);
router.post('/getPositions', verifyAdminToken, corporateData.getPositions);

router.post('/addMasterSubSkill', corporateData.addSkillsAsMasterEntry);
router.get('/getSubSkill', corporateData.getAllSkills);
router.post('/AssignMasterSkills', corporateData.assignSkillsAsMasterEntry);

router.get('/getInterests', corporateData.getInterests);
router.post('/addMasterInterest', corporateData.addInterestsAsMasterEntry);
router.post('/AssignMasterInterst', corporateData.assignInterestAsMasterEntry);

router.post('/addMasterCompanies', corporateData.addCompaniesAsMasterEntry);
router.post('/addMasterPositions', corporateData.addPositionsAsMasterEntry);

router.post(
  '/AssignMasterCompanies',
  corporateData.assignComapniesAsMasterEntry,
);
router.post(
  '/AssignMasterPosition',
  corporateData.assignPositionsAsMasterEntry,
);

router.get('/getCompanies', corporateData.getCompaniesList);
router.get('/getPositions', corporateData.getUserRole);

module.exports = router;
