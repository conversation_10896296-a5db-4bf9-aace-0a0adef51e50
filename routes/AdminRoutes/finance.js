const express = require('express');
const router = express.Router();
const finance = require('../../controllers/Admin/finance');
const { verifyAdminToken } = require('../../utils/jwtUtils');

router.post('/add-edit-promo', verifyAdminToken, finance.AddEditPromoCode);
router.post('/get-promo', verifyAdminToken, finance.getPromoCodes);
router.post('/delete-promo', verifyAdminToken, finance.deletePromocode);

module.exports = router;
