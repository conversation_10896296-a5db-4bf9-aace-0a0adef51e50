const express = require('express');
const router = express.Router();
const dashboardApis = require('../../controllers/Admin/dashoard');
const { verifyAdminToken } = require('../../utils/jwtUtils');


router.post('/getErrorLogs', verifyAdminToken, dashboardApis.getErrorLogs);
router.post('/updateStatus', verifyAdminToken, dashboardApis.updateResolveStatus);
router.post('/deleteErrorLogs', verifyAdminToken, dashboardApis.deleteErrorLogs);



module.exports = router;
