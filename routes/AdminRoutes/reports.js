const express = require('express');
const reports = require('../../controllers/Admin/reports');
const { verifyAdminToken } = require('../../utils/jwtUtils');
const router = express.Router();

router.post('/get-reports', verifyAdminToken, reports.GetReportsList);
router.post('/suspend-user', verifyAdminToken, reports.suspendUser);
router.post('/un-suspend-user', verifyAdminToken, reports.unSuspendUser);

router.post('/get-contact-us-data', verifyAdminToken, reports.GetContactList);

module.exports = router;
