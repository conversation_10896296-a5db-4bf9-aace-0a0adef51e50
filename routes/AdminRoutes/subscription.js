const express = require('express');
const subscription = require('../../controllers/Admin/subscription');
const { verifyAdminToken } = require('../../utils/jwtUtils');
const router = express.Router();

router.post(
  '/get-subscriptions',
  verifyAdminToken,
  subscription.GetSubscriptionList,
);
router.post(
  '/add-edit-subscription',
  verifyAdminToken,
  subscription.AddEditSubscription,
);

router.post('/get-features', verifyAdminToken, subscription.GetFeatureList);
router.post(
  '/add-remove-feature',
  verifyAdminToken,
  subscription.AddFeatureSubscription,
);

router.post('/add-features', verifyAdminToken, subscription.AddFeatures);
router.post(
  '/delete-subscription',
  verifyAdminToken,
  subscription.deleteSubscription,
);
router.post(
  '/get-all-features',
  verifyAdminToken,
  subscription.GetAllFeatureList,
);
router.post('/delete-features', verifyAdminToken, subscription.DeleteFeature);

module.exports = router;
