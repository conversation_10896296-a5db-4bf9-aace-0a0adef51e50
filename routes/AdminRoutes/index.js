const express = require('express');
const app = express();
const education = require('./education.js');
const corporate = require('./corporatedata.js');
const country = require('./countrydata.js');
const notification = require('./notification.js');
const reports = require('./reports.js');

const users = require('./users.js');

const auth = require('./auth.js');
const audit = require('./audit.js');
const fiance = require('./finance.js');
const subscription = require('./subscription.js');
const importData = require('./importdata.js');
const dashoard = require('./dashboard.js');

app.use('', auth);
app.use('/users', users);
app.use('/education', education);
app.use('/corporate', corporate);
app.use('/country', country);
app.use('/notification', notification);
app.use('/reports', reports);
app.use('/audit', audit);
app.use('/finance', fiance);
app.use('/subscription', subscription);
app.use('/import', importData);
app.use('/dashbord', dashoard);
module.exports = app;
