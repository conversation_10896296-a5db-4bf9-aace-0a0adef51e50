const express = require('express');
const {
  sendNotification,
  getNotification,
  deleteNotification,
} = require('../../controllers/Admin/notification');
const { verifyAdminToken } = require('../../utils/jwtUtils');
// const users = require('../../controllers/Admin/users');
const router = express.Router();

router.post('/send-notification', verifyAdminToken, sendNotification);
router.post('/get-notification', verifyAdminToken, getNotification);
router.post('/delete-notification', verifyAdminToken, deleteNotification);

module.exports = router;
