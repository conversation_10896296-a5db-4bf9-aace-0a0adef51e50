const express = require('express');
const router = express.Router();
const auth = require('../../controllers/Admin/authAdmin');
const { celebrate } = require('celebrate');
const authSchema = require('../../validators/AdminValidator/adminAuth');
const { verifyAdminToken } = require('../../utils/jwtUtils');

router.post(
  '/sign-in',
  celebrate({
    body: authSchema.loginSchema,
  }),
  auth.login,
);
router.post('/delete-masterEntry', verifyAdminToken, auth.deleteMasterEntry);
router.post('/verifyOtp', auth.verifyOtp);
router.get('/resendOtp', auth.resendOtp);

module.exports = router;
