const express = require('express');
const users = require('../../controllers/Admin/users');
const { verifyAdminToken } = require('../../utils/jwtUtils');
const router = express.Router();

router.post('/get-users', verifyAdminToken, users.usersList);
router.post('/get-users-detail', verifyAdminToken, users.usersDetail);
router.post('/get-all-users', verifyAdminToken, users.usersAllList);
router.get('/get-user-heat-map', verifyAdminToken, users.getUserHeatMap);
router.post('/get-user-pitch-stats', verifyAdminToken, users.getUserPitchStats);
router.get('/get-user_pitch-list', verifyAdminToken, users.getUserPitchList);
router.post('/edit-user', verifyAdminToken, users.updateUser);
router.get('/get-designation', verifyAdminToken, users.getAllDesignation);
router.get('/get-experience', verifyAdminToken, users.getExperience);
router.get('/get-languages', verifyAdminToken, users.getLanguageList);

module.exports = router;
