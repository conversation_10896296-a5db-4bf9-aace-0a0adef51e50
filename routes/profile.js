const express = require('express');
const router = express.Router();
const profile = require('../controllers/profile');
const { celebrate } = require('celebrate');
const profileSchema = require('../validators/profile');
const { verifyToken } = require('../utils/jwtUtils');

const authSchema = require('../validators/auth');

router.get('/get-details', verifyToken, profile.getProfileDetails);

router.get('/get-settings', verifyToken, profile.getProfileSettings);

router.post(
  '/update-settings',
  verifyToken,
  celebrate({
    body: profileSchema.ProfileSettingSchema,
  }),
  profile.updateProfileSettings,
);

router.post(
  '/add-location',
  verifyToken,
  celebrate({ body: profileSchema.userLocationSchema }),
  profile.insertUserLocation,
);

router.post(
  '/add-session',
  verifyToken,
  celebrate({ body: profileSchema.userSessionSchema }),
  profile.insertUserSession,
);

router.post(
  '/verify',
  verifyToken,
  celebrate({
    body: profileSchema.verifySchema,
  }),
  profile.verify,
);
router.get('/get-details-by-id', verifyToken, profile.getProfileDetailsById);
router.get('/get-Institude', verifyToken, profile.getInstitude);
router.get('/get-Degrees', verifyToken, profile.getDegreelist);
router.get('/get-field-study', verifyToken, profile.getFieldList);
router.get('/get-languages', verifyToken, profile.getLanguageList);
router.get('/get-companies', verifyToken, profile.getCompaniesList);
router.get('/get-employment', verifyToken, profile.getEmploymentTypeList);
router.get('/get-job-location', verifyToken, profile.getLocationTypeList);
router.get('/get-job-position', verifyToken, profile.getUserRole);
router.get('/get-proficiency', verifyToken, profile.proficiencyLevel);
// router.post(
//   '/get-profile-details-list',
//   verifyToken,
//   celebrate({
//     body: profileSchema.getProfileDetailsList,
//   }),
//   profile.getProfileDetailsList,
// );
router.post(
  '/add-Education',
  verifyToken,
  celebrate({
    body: profileSchema.addEducation,
  }),
  profile.addEducation,
);

router.post(
  '/update-Education',
  verifyToken,
  celebrate({
    body: profileSchema.updateEducation,
  }),
  profile.updateEducation,
);

router.delete(
  '/delete-Education',
  verifyToken,
  celebrate({
    query: profileSchema.deleteEducation,
  }),
  profile.deleteEducation,
);

router.get(
  '/get-interests',
  verifyToken,
  celebrate({
    query: profileSchema.getInterests,
  }),
  profile.getInterests,
);

router.post(
  '/add-interests',
  verifyToken,
  celebrate({
    body: profileSchema.addInterests,
  }),
  profile.addInterests,
);

router.post(
  '/add-other-interest',
  verifyToken,
  celebrate({
    body: profileSchema.addOtherInterest,
  }),
  profile.addOtherInterest,
);

router.delete(
  '/delete-other-interest',
  verifyToken,
  celebrate({
    query: profileSchema.deleteOtherInterest,
  }),
  profile.deleteOtherInterest,
);

router.post(
  '/add-Experience',
  verifyToken,
  celebrate({
    body: profileSchema.addExperience,
  }),
  profile.addExperience,
);
router.post(
  '/update-Experience',
  verifyToken,
  celebrate({
    body: profileSchema.updateExperience,
  }),
  profile.updateExperience,
);
router.delete(
  '/delete-Experience',
  verifyToken,
  celebrate({
    query: profileSchema.deleteExperience,
  }),
  profile.deleteExperience,
);

router.post(
  '/add-language',
  verifyToken,
  celebrate({
    body: profileSchema.addLanguage,
  }),
  profile.addLanguage,
);
router.post(
  '/update-language',
  verifyToken,
  celebrate({
    body: profileSchema.updateLanguage,
  }),
  profile.updateLanguage,
);

router.delete(
  '/delete-language',
  verifyToken,
  celebrate({
    query: profileSchema.deleteLanguage,
  }),
  profile.deleteLanguage,
);

router.post(
  '/update-sequance',
  verifyToken,
  celebrate({
    body: profileSchema.updateSequance,
  }),

  profile.updateSeqance,
);
router.post(
  '/add-links',
  verifyToken,
  celebrate({
    body: profileSchema.addLinks,
  }),
  profile.addLinks,
);
router.get('/get_link_type', verifyToken, profile.listLinkType);
router.delete(
  '/delete-links',
  verifyToken,
  celebrate({
    query: profileSchema.deleteLinks,
  }),
  profile.deleteLinks,
);
router.post(
  '/update-links',
  verifyToken,
  celebrate({
    body: profileSchema.updateLinks,
  }),
  profile.updateLinks,
);
router.get(
  '/get-media',
  verifyToken,
  celebrate({
    query: profileSchema.listMedia,
  }),
  profile.listMedia,
);
router.post(
  '/update-media',
  verifyToken,
  celebrate({
    body: profileSchema.updateMedia,
  }),
  profile.updateMedia,
);
router.post('/matched-percentage', verifyToken, profile.matchedPercentage);

router.delete('/delete-pitch', verifyToken, profile.deletePitch);

router.post(
  '/addPhone',
  verifyToken,
  celebrate({
    body: authSchema.sendOtpSchema,
  }),
  profile.addPhoneAndCode,
);

router.post(
  '/connect-github',
  verifyToken,
  celebrate({
    body: profileSchema.syncGithub,
  }),
  profile.syncGithub,
);

router.get('/disconnect-github', verifyToken, profile.unsyncGithub);

module.exports = router;
