const express = require('express');
const router = express.Router();
const notification = require('../controllers/notification');
const { celebrate } = require('celebrate');
const notificationSchema = require('../validators/notification');
const { verifyToken } = require('../utils/jwtUtils');

router.post(
  '/get',
  verifyToken,
  celebrate({
    body: notificationSchema.notificationlist,
  }),
  notification.notificationlist,
);
router.post(
  '/read',
  verifyToken,
  // celebrate({
  //   body: notificationSchema.notificationRead,
  // }),
  notification.notificationRead,
);

router.post(
  '/delete',
  verifyToken,
  celebrate({
    body: notificationSchema.notificationDelete,
  }),
  notification.notificationDelete,
);

router.post(
  '/send-chat',
  verifyToken,
  celebrate({
    body: notificationSchema.chatNotification,
  }),
  notification.chat,
);

router.get(
  '/get-unread-notification',
  verifyToken,

  notification.notificationUnreadReadCount,
);
module.exports = router;
