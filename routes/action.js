const actionsSchema = require('../validators/action');
const actions = require('../controllers/action');
const express = require('express');
const { verifyToken } = require('../utils/jwtUtils');
const { celebrate } = require('celebrate');
const router = express.Router();

router.post(
  '/report',
  verifyToken,
  celebrate({ body: actionsSchema.reportSchema }),
  actions.reportUser,
);

router.get('/report-reasons', verifyToken, actions.getReportReason);

router.post(
  '/block',
  verifyToken,
  celebrate({ body: actionsSchema.blockSchema }),
  actions.blockUser,
);
router.post(
  '/unblock',
  verifyToken,
  celebrate({ body: actionsSchema.unblockSchema }),
  actions.unblockUser,
);
router.get('/block-list', verifyToken, actions.getBlockList);

router.post(
  '/match-list',
  verifyToken,
  celebrate({ body: actionsSchema.matchListSchema }),
  actions.matchList,
);

router.post(
  '/like-list',
  verifyToken,
  celebrate({ body: actionsSchema.likelistSchema }),
  actions.likeList,
);

router.post('/start-chat', verifyToken, actions.startChat);

router.post(
  '/remove-match',
  verifyToken,
  celebrate({ body: actionsSchema.removeMatch }),
  actions.unMatch,
);
router.post(
  '/remove-like',
  verifyToken,
  celebrate({ body: actionsSchema.removeLike }),
  actions.removeLikeSuperLike,
);
router.post(
  '/user-search',
  verifyToken,
  celebrate({ body: actions.userSearch }),
  actions.userSearch,
);

router.post(
  '/view-pitch',
  verifyToken,
  celebrate({ body: actionsSchema.viewPitchSchema }),
  actions.viewPitch,
);

router.post(
  '/view-pitch-details',
  verifyToken,
  celebrate({ body: actionsSchema.viewPitchDetailsSchema }),
  actions.viewPitchDetails,
);

router.get('/rewind', verifyToken, actions.revertLastAction);
router.get('/access-token', verifyToken, actions.accessTokenGenerate);
router.post(
  '/update-video-call-history',
  verifyToken,
  celebrate({ body: actionsSchema.updateVideoCallHistory }),
  actions.updateVideoCallHistory,
);

router.get('/get-block-status', verifyToken, actions.blockStatus);

module.exports = router;
