const express = require('express');
const { verifyToken } = require('../utils/jwtUtils');
const subscription = require('../controllers/subscription');
const validator = require('../validators/subscription');
const { celebrate } = require('celebrate');
const router = express.Router();

router.get(
  '/get-subscription-plans',
  verifyToken,
  subscription.getSubscriptionPlans,
);

router.post(
  '/add-subscription-plan',
  verifyToken,
  celebrate({
    body: validator.addPlan,
  }),
  subscription.addSubscriptionPlan,
);

router.post(
  '/validate-promo-code',
  verifyToken,
  celebrate({
    body: validator.validatePromoCode,
  }),
  subscription.validatePromoCode,
);

router.post(
  '/subscription-webhook',
  celebrate({
    body: validator.validateWebhookPayload,
  }),
  subscription.subscriptionWebhook,
);

module.exports = router;
