const express = require('express');
const router = express.Router();
const auth = require('../controllers/auth');
const { celebrate } = require('celebrate');
const authSchema = require('../validators/auth');
const { verifyToken } = require('../utils/jwtUtils');

router.post(
  '/sign-up',
  celebrate({
    body: authSchema.registerSchema,
    headers: authSchema.headerSchema,
  }),
  auth.register,
);

router.post(
  '/profile-setup',
  verifyToken,
  celebrate({
    body: authSchema.profileSetupSchema,
  }),
  auth.profileSetup,
);

router.post(
  '/add-skills',
  verifyToken,
  celebrate({
    body: authSchema.addSkillsSchema,
  }),
  auth.addSkills,
);

router.post(
  '/add-profile-pic',
  verifyToken,
  celebrate({
    body: authSchema.addProfilePicSchema,
  }),
  auth.updateProfilePic,
);

router.post(
  '/add-profile-pitch',
  verifyToken,
  celebrate({
    body: authSchema.addProfilePitchSchema,
  }),
  auth.updateProfilePitch,
);
router.post(
  '/add-get-pitch-script',
  verifyToken,
  // celebrate({
  //   body: authSchema.addProfilePitchSchema,
  // }),
  auth.addGetScript,
);

router.post(
  '/send-otp',
  verifyToken,
  celebrate({
    body: authSchema.sendOtpSchema,
  }),
  auth.sendOtp,
);

router.post(
  '/verfiy-otp',
  verifyToken,
  celebrate({
    body: authSchema.verifyOtpSchema,
  }),
  auth.verifyOtp,
);

router.post(
  '/add-subscription',
  verifyToken,
  celebrate({
    body: authSchema.addSubscription,
  }),
  auth.addSubscription,
);

router.get(
  '/get-skills',
  verifyToken,
  celebrate({
    params: authSchema.getSkillsSchema,
  }),
  auth.getSkills,
),
  router.post(
    '/looking-for',
    verifyToken,
    celebrate({
      body: authSchema.getLookingForSchema,
    }),
    auth.lookingUpFor,
  );

router.post(
  '/add-about',
  verifyToken,
  celebrate({
    body: authSchema.addAboutSchema,
  }),
  auth.addAbout,
);

router.post(
  '/add-other-skill',
  verifyToken,
  celebrate({ body: authSchema.addOtherSkillSchema }),
  auth.addOtherSkill,
);

router.post(
  '/remove-other-skill',
  verifyToken,
  celebrate({ body: authSchema.removeOtherSkillSchema }),
  auth.removeOtherSkill,
);

router.post('/logout', verifyToken, auth.logout);
router.post(
  '/delete-account',
  verifyToken,
  celebrate({ body: authSchema.deleteAccount }),
  auth.deleteAccount,
);

module.exports = router;
