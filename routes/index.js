const express = require('express');
const router = express.Router();
const common = require('../controllers/index');
const { verifyToken } = require('../utils/jwtUtils');
const { celebrate } = require('celebrate');
const commonSchema = require('../validators/index');

router.get('/get-experience', verifyToken, common.getExperience);

router.get(
  '/get-designation',
  verifyToken,
  celebrate({ params: commonSchema.getDesignationSchema }),
  common.getDesignation,
);

module.exports = router;
