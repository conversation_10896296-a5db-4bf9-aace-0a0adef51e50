const express = require('express');
const contactSchema = require('../validators/WebSite');
const contact = require('../controllers/webSite');
const { celebrate } = require('celebrate');
const router = express.Router();

router.post(
  '/contact-us-email',
  celebrate({
    body: contactSchema.contactUs,
  }),
  contact.validateAndSendContactForm,
);
router.post('/validate-capcha', contact.validateCaptcha);

module.exports = router;
