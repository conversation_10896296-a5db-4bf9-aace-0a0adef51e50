const apn = require('apn');
const { voipApiKey, voipKeyId, voipTeamId, voipBundleId } = require('.');

const apnProvider = new apn.Provider({
  token: {
    key: voipApiKey, // from Apple Developer
    keyId: voipKeyId,
    teamId: voipTeamId,
  },
  production: false, // true for production
});

exports.sendVoipNotification = async (voipToken, payload) => {
  const notification = new apn.Notification({
    topic: voipBundleId,
    pushType: 'voip',
    payload: payload,
    contentAvailable: 1,
    priority: 10,
  });

  try {
    const result = await apnProvider.send(notification, voipToken);
    console.log('APNs response:', result);
    return result;
  } catch (err) {
    console.error('Error sending VoIP notification:', err);
    throw err;
  }
};
