module.exports = {
  PORT: process.env.PORT,
  dbHost: process.env.NODE_DATABASE_HOST,
  dbUser: process.env.NODE_DATABASE_USER,
  dbPassword: process.env.NODE_DATABASE_PASSWORD,
  dbName: process.env.NODE_DATABASE_NAME,
  dbPort: process.env.NODE_DATABASE_PORT,
  jwtAccessToken: process.env.NODE_JWT_SECRET_KEY,
  mailHost: process.env.NODE_MAIL_HOST,
  mailPort: process.env.NODE_MAIL_PORT,
  mailService: process.env.NODE_MAIL_SERVICE,
  mailUser: process.env.NODE_MAIL_USER,
  mailPassword: process.env.NODE_MAIL_PASSWORD,
  twilioPhone: process.env.TWILIO_PHONE_NUMBER,
  twilioAccountSid: process.env.TWILIO_ACCOUNT_SID,
  twilioAuthToken: process.env.TWILIO_AUTH_TOKEN,
  twilioServiceSid: process.env.TWILIO_SERVICE_SID,
  awsAccessKey: process.env.AWS_ACCESS_KEY_ID,
  awsSecretKey: process.env.AWS_SECRET_ACCESS_KEY,
  awsRegion: process.env.AWS_REGION,
  awsBucketName: process.env.AWS_BUCKET_NAME,
  awsCdnPath: process.env.AWS_CDN_PATH,
  sendgridApiKey: process.env.SENDGRID_API_KEY,
  algoliaAppId: process.env.ALGOLIA_APP_ID,
  algoliaAdminApiKey: process.env.ALGOLIA_ADMIN_API_KEY,
  algoliaApiKey: process.env.ALGOLIA_API_KEY,
  algoliaIndexName: process.env.ALGOLIA_INDEX_NAME,
  voipApiKey: process.env.VOIP_API_KEY,
  voipKeyId: process.env.VOIP_KEY_ID,
  voipTeamId: process.env.VOIP_TEAM_ID,
  voipBundleId: process.env.VOIP_BUNDLE_ID,
  // Apple App Store Server Notifications configuration
  appleWebhookKey: process.env.APPLE_WEBHOOK_KEY,
  appleWebhookKeyId: process.env.APPLE_WEBHOOK_KEY_ID,
  appleWebhookIssuerId: process.env.APPLE_WEBHOOK_ISSUER_ID,
  appleWebhookAppBundleId: process.env.APPLE_WEBHOOK_APP_BUNDLE_ID,
};
