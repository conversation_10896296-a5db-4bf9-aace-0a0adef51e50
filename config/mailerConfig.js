const sgMail = require('@sendgrid/mail');
const config = require('../config');

class Mailer {
  constructor() {
    sgMail.setApiKey(config.sendgridApiKey);
  }

  sendMail(msg, callback) {
    // const msg = {
    //   to,
    //   from,
    //   subject,
    //   html,
    // };
    sgMail
      .send(msg)
      .then(([response]) => {
        const info = {
          statusCode: response.statusCode,
          headers: response.headers,
        };
        if (callback) callback(null, info);
      })
      .catch((error) => {
        if (callback) callback(error, null);
      });
  }
}

module.exports = new Mailer();
