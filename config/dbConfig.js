const mysql = require('mysql2/promise');
const config = require('./index');

let pool;

// Function to create a database connection pool
exports.createPool = () => {
  if (!pool) {
    try {
      pool = mysql.createPool({
        host: config.dbHost,
        user: config.dbUser,
        password: config.dbPassword,
        database: config.dbName,
        port: config.dbPort,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 50, // Adjust based on use case
      });
      console.log('DB POOL CREATED SUCCESSFULLY!');
    } catch (error) {
      console.error('Error creating DB pool:', error.message);
      throw error;
    }
  } else {
    console.log('DB POOL ALREADY EXISTS!');
  }
};

// Function to execute a query
exports.executeQuery = async (sql, values = []) => {
  let connection;
  try {
    connection = await pool.getConnection();
    const [result] = await connection.query(sql, values);
    return result;
  } catch (error) {
    console.error('Error executing query:', error.stack || error.message);
    throw error;
  } finally {
    if (connection) {
      connection.release();
      console.log('Connection released back to pool!');
    }
  }
};

// Function to respond with JSON
exports.response = (res, status, message, data = [], statusBool = false) => {
  return res.status(status).json({ status: statusBool, message, data });
};

// Function to gracefully close the pool
exports.closePool = async () => {
  if (pool) {
    await pool.end();
    console.log('DB POOL CLOSED SUCCESSFULLY!');
  }
};

// Initialize the connection pool when the application starts
exports.createPool();

// Graceful shutdown on termination signals
process.on('SIGINT', async () => {
  console.log('Gracefully shutting down...');
  await exports.closePool();
  process.exit(0);
});
