{"/": [{"endpoint": "/sign-up", "actionType": "User SignUp / Login"}, {"endpoint": "/profile-setup", "actionType": "Profile Setup"}, {"endpoint": "/add-skills", "actionType": "Add Skills"}, {"endpoint": "/add-profile-pic", "actionType": "Add Profile Picture"}, {"endpoint": "/add-profile-pitch", "actionType": "Add Profile Pitch"}, {"endpoint": "/send-otp", "actionType": "Send OTP"}, {"endpoint": "/verfiy-otp", "actionType": "Verify OTP"}, {"endpoint": "/get-skills", "actionType": "Get Skills"}, {"endpoint": "/looking-for", "actionType": "Looking For"}, {"endpoint": "/add-about", "actionType": "Add About"}, {"endpoint": "/add-other-skill", "actionType": "Add Other Skill"}, {"endpoint": "/remove-other-skill", "actionType": "Remove Other Skill"}, {"endpoint": "/logout", "actionType": "User <PERSON>"}, {"endpoint": "/delete-account", "actionType": "Delete Account"}], "/common": [{"endpoint": "/get-experience", "actionType": "Get Experience List"}, {"endpoint": "/get-designation", "actionType": "Get Designation List"}], "/profile": [{"endpoint": "/get-details", "actionType": "Get Profile Details"}, {"endpoint": "/get-settings", "actionType": "Get Profile Settings"}, {"endpoint": "/update-settings", "actionType": "Update Profile Settings"}, {"endpoint": "/add-location", "actionType": "Add User Location"}, {"endpoint": "/add-session", "actionType": "Add User Session"}, {"endpoint": "/verify", "actionType": "Verify User Profile"}, {"endpoint": "/get-details-by-id", "actionType": "Get Profile Details by ID"}, {"endpoint": "/get-Institude", "actionType": "Get Institutes List"}, {"endpoint": "/get-Degrees", "actionType": "Get Degrees List"}, {"endpoint": "/get-field-study", "actionType": "Get Fields of Study"}, {"endpoint": "/get-languages", "actionType": "Get Available Languages"}, {"endpoint": "/get-companies", "actionType": "Get Companies List"}, {"endpoint": "/get-employment", "actionType": "Get Employment Types"}, {"endpoint": "/get-job-location", "actionType": "Get Job Locations"}, {"endpoint": "/get-job-position", "actionType": "Get Job Positions"}, {"endpoint": "/get-proficiency", "actionType": "Get Proficiency Levels"}, {"endpoint": "/add-Education", "actionType": "Add Education Details"}, {"endpoint": "/update-Education", "actionType": "Update Education Details"}, {"endpoint": "/delete-Education", "actionType": "Delete Education Details"}, {"endpoint": "/get-interests", "actionType": "Get User Interests"}, {"endpoint": "/add-interests", "actionType": "Add Interests"}, {"endpoint": "/add-other-interest", "actionType": "Add Other Interests"}, {"endpoint": "/delete-other-interest", "actionType": "Delete Other Interests"}, {"endpoint": "/add-Experience", "actionType": "Add Work Experience"}, {"endpoint": "/update-Experience", "actionType": "Update Work Experience"}, {"endpoint": "/delete-Experience", "actionType": "Delete Work Experience"}, {"endpoint": "/add-language", "actionType": "Add Language"}, {"endpoint": "/update-language", "actionType": "Update Language"}, {"endpoint": "/delete-language", "actionType": "Delete Language"}, {"endpoint": "/update-sequance", "actionType": "Update Profile Sequence"}, {"endpoint": "/add-links", "actionType": "Add Profile Links"}, {"endpoint": "/get_link_type", "actionType": "Get Link Types"}, {"endpoint": "/delete-links", "actionType": "Delete Profile Links"}, {"endpoint": "/update-links", "actionType": "Update Profile Links"}, {"endpoint": "/get-media", "actionType": "Get User Media"}, {"endpoint": "/update-media", "actionType": "Update User Media"}, {"endpoint": "/matched-percentage", "actionType": "Get Matched Percentage"}, {"endpoint": "/delete-pitch", "actionType": "Delete Profile Pitch"}, {"endpoint": "/addPhone", "actionType": "Add Phone Number"}, {"endpoint": "/connect-github", "actionType": "Connect GitHub Account"}, {"endpoint": "/disconnect-github", "actionType": "Disconnect GitHub Account"}], "/actions": [{"endpoint": "/report", "actionType": "Report User"}, {"endpoint": "/report-reasons", "actionType": "Get Report Reasons"}, {"endpoint": "/block", "actionType": "Block User"}, {"endpoint": "/unblock", "actionType": "Unblock User"}, {"endpoint": "/block-list", "actionType": "Get Block List"}, {"endpoint": "/match-list", "actionType": "Get Match List"}, {"endpoint": "/like-list", "actionType": "Get Like List"}, {"endpoint": "/start-chat", "actionType": "Start Chat"}, {"endpoint": "/remove-match", "actionType": "Remove Match"}, {"endpoint": "/user-search", "actionType": "Search User"}, {"endpoint": "/view-pitch", "actionType": "View Pitch"}, {"endpoint": "/view-pitch-details", "actionType": "View Pitch Details"}, {"endpoint": "/rewind", "actionType": "<PERSON>ert <PERSON>"}, {"endpoint": "/access-token", "actionType": "Generate Access Token"}, {"endpoint": "/update-video-call-history", "actionType": "Update Video Call History"}], "/home": [{"endpoint": "/like-user", "actionType": "Like User"}, {"endpoint": "/show-user-likes", "actionType": "Show User Likes"}, {"endpoint": "/show-user-match", "actionType": "Show User Match"}, {"endpoint": "/show-user-matches", "actionType": "Show User Matches"}, {"endpoint": "/settings-config", "actionType": "Update Settings Configuration"}, {"endpoint": "/near-by", "actionType": "Find Nearby Users"}, {"endpoint": "/best-matches", "actionType": "Get Best Matches"}, {"endpoint": "/best-map-matches", "actionType": "Get Best Matches on Map"}, {"endpoint": "/top-users", "actionType": "Get Top Users"}, {"endpoint": "/rate-users", "actionType": "Rate Users"}], "/subscription": [{"endpoint": "/get-subscription-plans", "actionType": "Get Subscription Plans"}, {"endpoint": "/add-subscription-plan", "actionType": "Add Subscription Plan"}, {"endpoint": "/validate-promo-code", "actionType": "Validate Promo Code"}], "/notification": [{"endpoint": "/get", "actionType": "Get Notifications"}, {"endpoint": "/read", "actionType": "Mark Notification as Read"}, {"endpoint": "/delete", "actionType": "Delete Notification"}, {"endpoint": "/send-chat", "actionType": "Send Chat Notification"}], "/adminApi": []}