const {
  RekognitionClient,
  DetectModerationLabelsCommand,
  StartContentModerationCommand,
  StartFaceDetectionCommand,
  GetContentModerationCommand,
  GetFaceDetectionCommand,
} = require('@aws-sdk/client-rekognition');
const { awsAccessKey, awsRegion, awsSecretKey, awsBucketName } = require('.');
const { executeQuery } = require('./dbConfig');
const { getUserFcm, getUserData } = require('../utils');
const { notification } = require('../utils/firebase');
const mailer = require('./mailerConfig');
const { pitchStatusMail } = require('../utils/mailhelpers');
const videoProcessor = require('../services/simpleVideoProcessor');

// Configure AWS SDK v3
const rekognitionClient = new RekognitionClient({
  region: awsRegion,
  credentials: {
    accessKeyId: awsAccessKey,
    secretAccessKey: awsSecretKey,
  },
});

// Helper function to analyze images using Rekognition
const analyzeImage = async (s3Url) => {
  try {
    const s3Key = extractS3Key(s3Url);
    const command = new DetectModerationLabelsCommand({
      Image: {
        S3Object: {
          Bucket: awsBucketName,
          Name: s3Key,
        },
      },
    });

    const response = await rekognitionClient.send(command);
    return {
      status: response.ModerationLabels.length === 0 ? 'APPROVED' : 'REJECTED',
      moderationLabels: response.ModerationLabels,
    };
  } catch (error) {
    console.error('Error analyzing image:', error);
    throw new Error('Image analysis failed');
  }
};

const startVideoModeration = async (s3Url, skipProcessing = false) => {
  try {
    let processedUrl = s3Url;

    // Check if video needs processing and process it
    if (!skipProcessing && (await videoProcessor.needsProcessing(s3Url))) {
      console.log(
        '🔧 Video needs processing for AWS Rekognition compatibility...',
      );
      try {
        const processingResult = await videoProcessor.processVideo(s3Url);
        processedUrl = processingResult.processedUrl;
        console.log('✅ Video processed successfully, using processed version');
      } catch (processingError) {
        console.warn(
          '⚠️ Video processing failed, attempting with original:',
          processingError.message,
        );
        // Continue with original video if processing fails
      }
    }

    const s3Key = extractS3Key(processedUrl);

    // Log video details for debugging
    console.log('🎥 Starting video analysis:', {
      originalUrl: s3Url,
      processedUrl,
      s3Key,
      bucket: awsBucketName,
      fileExtension: s3Key.split('.').pop()?.toLowerCase(),
    });

    const videoParams = {
      Video: {
        S3Object: {
          Bucket: awsBucketName,
          Name: s3Key,
        },
      },
    };

    // Start content moderation
    console.log('📊 Starting content moderation...');
    const moderationCommand = new StartContentModerationCommand(videoParams);
    const moderationResponse = await rekognitionClient.send(moderationCommand);

    // Start face detection
    console.log('👤 Starting face detection...');
    const faceParams = {
      ...videoParams,
      FaceAttributes: 'ALL',
    };

    const faceCommand = new StartFaceDetectionCommand(faceParams);
    const faceResponse = await rekognitionClient.send(faceCommand);

    console.log('✅ Video analysis jobs started successfully:', {
      moderationJobId: moderationResponse.JobId,
      faceDetectionJobId: faceResponse.JobId,
    });

    return {
      success: true,
      moderationJobId: moderationResponse.JobId,
      faceDetectionJobId: faceResponse.JobId,
      originalUrl: s3Url,
      processedUrl: processedUrl,
    };
  } catch (error) {
    console.error('❌ Error starting video analysis:', {
      error: error.message,
      code: error.code,
      statusCode: error.statusCode,
      requestId: error.requestId,
    });

    // Determine error type with more specific handling
    let errorCode = 'ANALYSIS_FAILED';
    let errorMessage = error.message || 'Failed to start video analysis';
    let suggestions = [];

    if (error.code === 'InvalidParameterException') {
      errorCode = 'INVALID_VIDEO';
      errorMessage = 'The video file is corrupted or in an unsupported format';
      suggestions = [
        'Ensure video is in MP4, MOV, or AVI format',
        'Check if video file is corrupted',
        'Verify video codec is H.264 or H.265',
        'Ensure video duration is between 1 second and 2 hours',
      ];
    } else if (error.code === 'AccessDeniedException') {
      errorCode = 'ACCESS_DENIED';
      errorMessage = 'Permission denied for video analysis';
      suggestions = ['Check AWS credentials and S3 bucket permissions'];
    } else if (error.code === 'VideoTooLargeException') {
      errorCode = 'VIDEO_TOO_LARGE';
      errorMessage = 'Video file exceeds size limit (max 8GB)';
      suggestions = ['Compress video file or reduce resolution'];
    } else if (error.code === 'UnsupportedDocumentException') {
      errorCode = 'UNSUPPORTED_FORMAT';
      errorMessage = 'Video format not supported by AWS Rekognition';
      suggestions = [
        'Convert video to MP4 with H.264 codec',
        'Ensure video has proper metadata',
      ];
    }

    console.error('💡 Troubleshooting suggestions:', suggestions);

    return {
      success: false,
      error: errorCode,
      message: errorMessage,
      suggestions,
      details: {
        awsErrorCode: error.code,
        statusCode: error.statusCode,
        requestId: error.requestId,
      },
    };
  }
};

// Helper functions to get job results
const getContentModerationResult = async (jobId) => {
  try {
    const command = new GetContentModerationCommand({ JobId: jobId });
    return await rekognitionClient.send(command);
  } catch (error) {
    console.error('Error getting content moderation result:', error);
    throw error;
  }
};

const getFaceDetectionResult = async (jobId) => {
  try {
    const command = new GetFaceDetectionCommand({ JobId: jobId });
    return await rekognitionClient.send(command);
  } catch (error) {
    console.error('Error getting face detection result:', error);
    throw error;
  }
};

const validateVideoResults = (faceDetectionResult) => {
  try {
    let faces = faceDetectionResult.Faces || [];
    console.log(`Number of faces detected: ${faces.length}`);

    // Filter faces with confidence > 80 and duration covering most of the video
    faces = faces.filter((face) => {
      const confidence = face.Face.Confidence;
      const duration = face.Timestamp;
      console.log(
        `Face detected at ${duration}ms with ${confidence}% confidence`,
      );
      return confidence > 80;
    });

    console.log(`Number of high-confidence faces: ${faces.length}`);

    if (faces.length === 0) {
      return { isValid: false, reason: 'No clear faces detected in video' };
    }

    // Sort faces by timestamp to ensure chronological processing
    faces.sort((a, b) => (a.Timestamp || 0) - (b.Timestamp || 0));

    // Group faces that are likely the same person appearing multiple times
    const groupedFaces = groupSimilarFaces(faces);
    console.log(
      `Number of distinct faces after grouping: ${groupedFaces.length}`,
    );

    // If we have more than one group, try to merge them based on additional criteria
    if (groupedFaces.length > 1) {
      const mergedGroups = mergeSimilarGroups(groupedFaces);
      console.log(`Number of faces after merging: ${mergedGroups.length}`);

      if (mergedGroups.length > 1) {
        return {
          isValid: false,
          reason: 'Multiple distinct faces detected in video',
        };
      }
    }

    // Take the face with highest average confidence
    const primaryFace = groupedFaces[0].reduce((prev, current) =>
      prev.Face.Confidence > current.Face.Confidence ? prev : current,
    );

    // Log detailed gender information for debugging
    console.log('Gender detection details:', {
      hasGender: !!primaryFace.Face.Gender,
      genderValue: primaryFace.Face.Gender?.Value,
      genderConfidence: primaryFace.Face.Gender?.Confidence,
      allFacesGenderInfo: groupedFaces[0].map((f) => ({
        timestamp: f.Timestamp,
        gender: f.Face.Gender?.Value,
        confidence: f.Face.Gender?.Confidence,
      })),
    });

    // Try to get gender from any face in the group if primary face doesn't have it
    let gender = primaryFace.Face.Gender?.Value;
    let genderConfidence = primaryFace.Face.Gender?.Confidence || 0;

    if (!gender || genderConfidence < 60) {
      // Look for gender in other faces in the group
      const facesWithGender = groupedFaces[0].filter(
        (f) => f.Face.Gender?.Value && f.Face.Gender?.Confidence > 60,
      );

      if (facesWithGender.length > 0) {
        // Use the face with highest gender confidence
        const bestGenderFace = facesWithGender.reduce((prev, current) =>
          (prev.Face.Gender?.Confidence || 0) >
          (current.Face.Gender?.Confidence || 0)
            ? prev
            : current,
        );
        gender = bestGenderFace.Face.Gender.Value;
        genderConfidence = bestGenderFace.Face.Gender.Confidence;
        console.log('Using gender from alternative face:', {
          gender,
          confidence: genderConfidence,
          timestamp: bestGenderFace.Timestamp,
        });
      }
    }

    console.log(
      `Primary face gender: ${gender || 'Unknown'} (${genderConfidence}% confidence)`,
    );

    if (!gender || genderConfidence < 60) {
      return {
        isValid: false,
        reason: 'Gender could not be determined with sufficient confidence',
      };
    }

    if (!['Male', 'Female'].includes(gender)) {
      return { isValid: false, reason: `Gender '${gender}' not accepted` };
    }

    return {
      isValid: true,
      gender,
      genderConfidence,
    };
  } catch (error) {
    console.error('Error validating video results:', error);
    return { isValid: false, reason: 'Validation error occurred' };
  }
};

// Helper function to merge similar groups
const mergeSimilarGroups = (groups) => {
  if (groups.length <= 1) return groups;

  const merged = [];
  const processed = new Set();

  for (let i = 0; i < groups.length; i++) {
    if (processed.has(i)) continue;

    const currentGroup = [...groups[i]];
    processed.add(i);

    for (let j = i + 1; j < groups.length; j++) {
      if (processed.has(j)) continue;

      const group1 = groups[i];
      const group2 = groups[j];

      // Get representative faces from each group
      const face1 = group1[0].Face;
      const face2 = group2[0].Face;

      // Calculate average bounding box for each group
      const avgBox1 = calculateAverageBoundingBox(group1);
      const avgBox2 = calculateAverageBoundingBox(group2);

      // Use more lenient thresholds for high confidence groups
      const isHighConfidence =
        face1.Confidence > 99.9 && face2.Confidence > 99.9;
      const boundingBoxThreshold = isHighConfidence ? 0.5 : 0.4;
      const genderConfidenceThreshold = isHighConfidence ? 40 : 30;

      // Check if groups are similar enough to merge
      const similarBoundingBox =
        Math.abs(avgBox1.Left - avgBox2.Left) < boundingBoxThreshold &&
        Math.abs(avgBox1.Top - avgBox2.Top) < boundingBoxThreshold &&
        Math.abs(avgBox1.Width - avgBox2.Width) < boundingBoxThreshold &&
        Math.abs(avgBox1.Height - avgBox2.Height) < boundingBoxThreshold;

      // Check if both groups have gender information
      const hasGenderInfo = face1.Gender && face2.Gender;
      const similarGender = hasGenderInfo
        ? face1.Gender.Value === face2.Gender.Value &&
          Math.abs(face1.Gender.Confidence - face2.Gender.Confidence) <
            genderConfidenceThreshold
        : true;

      // Check time overlap between groups
      const timeOverlap = checkTimeOverlap(group1, group2);

      if (similarBoundingBox && similarGender && timeOverlap) {
        currentGroup.push(...group2);
        processed.add(j);
      }
    }

    merged.push(currentGroup);
  }

  return merged;
};

// Helper function to calculate average bounding box
const calculateAverageBoundingBox = (faces) => {
  const sum = faces.reduce(
    (acc, face) => {
      const box = face.Face.BoundingBox;
      return {
        Left: acc.Left + box.Left,
        Top: acc.Top + box.Top,
        Width: acc.Width + box.Width,
        Height: acc.Height + box.Height,
      };
    },
    { Left: 0, Top: 0, Width: 0, Height: 0 },
  );

  return {
    Left: sum.Left / faces.length,
    Top: sum.Top / faces.length,
    Width: sum.Width / faces.length,
    Height: sum.Height / faces.length,
  };
};

// Helper function to check time overlap between groups
const checkTimeOverlap = (group1, group2) => {
  const times1 = group1.map((f) => f.Timestamp || 0);
  const times2 = group2.map((f) => f.Timestamp || 0);

  const min1 = Math.min(...times1);
  const max1 = Math.max(...times1);
  const min2 = Math.min(...times2);
  const max2 = Math.max(...times2);

  // Check if the time ranges overlap
  return !(max1 < min2 || max2 < min1);
};

// Helper function to group similar faces that are likely the same person
const groupSimilarFaces = (faces) => {
  const groups = [];
  const processedFaces = new Set();

  // Sort faces by timestamp to ensure chronological processing
  faces.sort((a, b) => (a.Timestamp || 0) - (b.Timestamp || 0));

  faces.forEach((face, index) => {
    // Skip if this face has already been processed
    if (processedFaces.has(index)) return;

    const currentGroup = [face];
    processedFaces.add(index);

    // Compare with all subsequent faces
    for (let i = index + 1; i < faces.length; i++) {
      if (processedFaces.has(i)) continue;

      const currentFace = face.Face;
      const compareFace = faces[i].Face;

      // Calculate time difference
      const timeDifference = Math.abs(
        (face.Timestamp || 0) - (faces[i].Timestamp || 0),
      );

      // For very high confidence faces (>99.9%), use more lenient thresholds
      const isHighConfidence =
        currentFace.Confidence > 99.9 && compareFace.Confidence > 99.9;

      // Enhanced similarity check with dynamic thresholds based on confidence
      const boundingBoxThreshold = isHighConfidence ? 0.5 : 0.4; // More lenient for high confidence
      const genderConfidenceThreshold = isHighConfidence ? 40 : 30; // More lenient for high confidence

      const similarBoundingBox =
        Math.abs(currentFace.BoundingBox.Left - compareFace.BoundingBox.Left) <
          boundingBoxThreshold &&
        Math.abs(currentFace.BoundingBox.Top - compareFace.BoundingBox.Top) <
          boundingBoxThreshold &&
        Math.abs(
          currentFace.BoundingBox.Width - compareFace.BoundingBox.Width,
        ) < boundingBoxThreshold &&
        Math.abs(
          currentFace.BoundingBox.Height - compareFace.BoundingBox.Height,
        ) < boundingBoxThreshold;

      // Check if both faces have gender information before comparing
      const hasGenderInfo = currentFace.Gender && compareFace.Gender;
      const similarGender = hasGenderInfo
        ? currentFace.Gender.Value === compareFace.Gender.Value &&
          Math.abs(
            currentFace.Gender.Confidence - compareFace.Gender.Confidence,
          ) < genderConfidenceThreshold
        : true;

      // Consider faces as same person if they are:
      // 1. Close in time (within 5 seconds for high confidence, 3 seconds otherwise) OR
      // 2. Have similar bounding boxes and gender
      const timeThreshold = isHighConfidence ? 5000 : 3000;
      const isCloseInTime = timeDifference < timeThreshold;
      const isSamePerson =
        isCloseInTime || (similarBoundingBox && similarGender);

      if (isSamePerson) {
        currentGroup.push(faces[i]);
        processedFaces.add(i);
      }
    }

    // Log group details for debugging
    if (currentGroup.length > 0) {
      const avgConfidence =
        currentGroup.reduce((sum, f) => sum + f.Face.Confidence, 0) /
        currentGroup.length;
      console.log(`Group ${groups.length + 1} details:`, {
        faceCount: currentGroup.length,
        avgConfidence: avgConfidence.toFixed(2),
        timeRange: `${currentGroup[0].Timestamp}ms - ${currentGroup[currentGroup.length - 1].Timestamp}ms`,
        gender: currentGroup[0].Face.Gender?.Value || 'Unknown',
        genderConfidence: currentGroup[0].Face.Gender?.Confidence || 0,
      });
    }

    groups.push(currentGroup);
  });

  return groups;
};

const isVideoSafe = async (moderationJobId, faceDetectionJobId) => {
  try {
    // Check if moderation job ID is provided
    if (!moderationJobId) {
      console.error('Missing moderation job ID');
      return { safe: false, reason: 'Missing content moderation job ID' };
    }

    // Get content moderation result
    const moderationResult = await getContentModerationResult(moderationJobId);

    // Check if face detection job ID is provided
    if (!faceDetectionJobId) {
      console.error('Missing face detection job ID');
      return { safe: false, reason: 'Missing face detection job ID' };
    }

    // Get face detection result
    console.log('📌 Processing face detection job ID:', faceDetectionJobId);
    const faceDetectionResult =
      await getFaceDetectionResult(faceDetectionJobId);

    // Check if jobs failed
    if (moderationResult.JobStatus === 'FAILED') {
      console.error('Moderation job failed:', {
        status: moderationResult.JobStatus,
        statusMessage: moderationResult.StatusMessage,
        videoMetadata: moderationResult.VideoMetadata,
      });
      return {
        safe: false,
        reason: `Content moderation failed: ${moderationResult.StatusMessage || 'Unknown error'}`,
      };
    }

    // Check face detection result
    if (faceDetectionResult.JobStatus === 'FAILED') {
      console.error('Face detection job failed:', {
        status: faceDetectionResult.JobStatus,
        statusMessage: faceDetectionResult.StatusMessage,
        videoMetadata: faceDetectionResult.VideoMetadata,
      });
      return {
        safe: false,
        reason: `Face detection failed: ${faceDetectionResult.StatusMessage || 'Unknown error'}`,
      };
    }

    // Check if moderation job succeeded
    if (moderationResult.JobStatus !== 'SUCCEEDED') {
      console.error(`Moderation job status: ${moderationResult.JobStatus}`);
      return { safe: false, reason: 'Content moderation not completed' };
    }

    // Check face detection job status
    if (faceDetectionResult.JobStatus !== 'SUCCEEDED') {
      console.error(
        `Face detection job status: ${faceDetectionResult.JobStatus}`,
      );
      return { safe: false, reason: 'Face detection not completed' };
    }

    // Log video metadata for debugging
    console.log('Video metadata:', {
      moderation: moderationResult.VideoMetadata,
      faceDetection: faceDetectionResult.VideoMetadata,
    });

    // Check content moderation labels
    const moderationLabels = moderationResult.ModerationLabels || [];
    if (moderationLabels.length > 0) {
      const issues = moderationLabels
        .filter((label) => label.ModerationLabel.Confidence > 50)
        .map(
          (label) =>
            `${label.ModerationLabel.Name} (${Math.round(label.ModerationLabel.Confidence)}%)`,
        );

      if (issues.length > 0) {
        console.log('Unsafe content detected:', issues.join(', '));
        return { safe: false, reason: 'Inappropriate content detected' };
      }
    }

    // Validate video requirements
    const validation = validateVideoResults(faceDetectionResult);
    if (!validation.isValid) {
      console.log(`Video validation failed: ${validation.reason}`);
      return { safe: false, reason: validation.reason };
    }

    console.log('Video is safe for use.');
    return {
      safe: true,
      gender: validation.gender,
      reason: 'Video passed all safety checks',
      moderationDetails: {
        moderationLabels: moderationLabels,
        faceCount: faceDetectionResult.Faces?.length || 0,
        gender: validation.gender,
        genderConfidence: validation.genderConfidence,
      },
    };
  } catch (error) {
    console.error('Error during video safety check:', error);
    return { safe: false, reason: 'Technical error during analysis' };
  }
};

// Function to check pending jobs
const checkPendingJobs = async () => {
  console.log('Checking pending video moderation jobs...');

  try {
    const pendingJobs = await executeQuery('CALL GetPendingJobIds();');
    if (!Array.isArray(pendingJobs[0]) || pendingJobs[0].length === 0) {
      console.log('No pending jobs found.');
      return;
    }

    console.log(`🔍 Found ${pendingJobs[0].length} pending jobs...`);

    for (const job of pendingJobs[0]) {
      // Require both moderation and face detection job IDs
      if (!job.job_id || !job.face_job_id) {
        console.warn('⚠️ Skipping job due to missing job IDs:', job);
        continue;
      }

      try {
        console.log(`📌 Processing job ID: ${job.job_id}`);

        const safetyCheck = await isVideoSafe(job.job_id, job.face_job_id);

        // Only proceed if we got a definitive result (safe or not safe)
        if (safetyCheck.reason.includes('not completed')) {
          console.log(`⏳ Job ${job.job_id} still in progress`);
          continue;
        }

        console.log('🔍 DETAILED SAFETY CHECK RESULT:');
        console.log('='.repeat(50));
        console.log(`👍 Video is safe: ${safetyCheck.safe ? 'YES' : 'NO'}`);
        console.log(`🔍 Reason: ${safetyCheck.reason}`);
        console.log(`👤 Gender: ${safetyCheck.gender || 'Not detected'}`);
        console.log('📊 Full result:', JSON.stringify(safetyCheck, null, 2));
        console.log('='.repeat(50));

        if (!safetyCheck.safe) {
          console.log(`❌ REJECTION REASON: ${safetyCheck.reason}`);
        }

        const jobIdStr = `'${job.job_id}'`;
        const status = safetyCheck.safe ? 1 : 5; // 1 for approved, 5 for rejected

        const moderationData = {
          validation: {
            isSafe: safetyCheck.safe,
            reason: safetyCheck.reason,
            gender: safetyCheck.gender,
            details: safetyCheck.moderationDetails || {},
          },
          timestamp: new Date().toISOString(),
          jobIds: {
            moderation: job.job_id,
            faceDetection: job.face_job_id,
          },
        };

        const sqlQuery = `CALL UpdateJobStatus(${jobIdStr}, ${status}, '${JSON.stringify(moderationData)}');`;
        const pitchDetails = await executeQuery(sqlQuery);

        // Only send notifications if we have a definitive result
        if (safetyCheck.safe !== undefined) {
          const fcmData = await getUserFcm(pitchDetails[0][0].user_id);
          console.log(
            fcmData,
            'fcmData',
            pitchDetails[0][0].media_id,
            'userId',
          );

          let notification_type = 'system_genrated';
          await executeQuery('CALL AddNotificationbyAdmins(?,?,?,?,?,?)', [
            pitchDetails[0][0].media_id,
            pitchDetails[0][0].user_id,
            'pitch',
            `Your Profile Pitch has been ${safetyCheck.safe ? 'Verified' : 'Rejected'}${!safetyCheck.safe ? `: ${safetyCheck.reason}` : ''}.`,
            'system_genrated',
            notification_type,
          ]);

          const userData = await getUserData(pitchDetails[0][0].user_id);
          console.log(pitchDetails[0][0], 'pitch url ');

          const { email: currentEmail, name } = userData[0];
          mailer.sendMail(
            pitchStatusMail(
              name,
              currentEmail,
              `Profile Pitch ${safetyCheck.safe ? 'Verified' : 'Rejected'}`,
              `${safetyCheck.safe ? 'Verified' : 'Rejected'}${!safetyCheck.safe ? `: ${safetyCheck.reason}` : ''}`,
            ),
            async (err, info) => {
              if (err) {
                console.error('Error sending mail:', err);
              }
              console.log(info, 'mail status');
            },
          );

          if (fcmData.length) {
            // Convert all data values to strings for FCM
            const notificationData = {
              pitch_id: pitchDetails[0][0].media_id.toString(),
              status: safetyCheck.safe ? 'Verified' : 'Rejected',
              reason: !safetyCheck.safe ? safetyCheck.reason : 'null',
              details: JSON.stringify(safetyCheck.moderationDetails || {}),
              timestamp: new Date().toISOString(),
              job_id: job.job_id.toString(),
              face_job_id: job.face_job_id
                ? job.face_job_id.toString()
                : 'null',
            };

            await notification(
              `Profile Pitch ${safetyCheck.safe ? 'Verified' : 'Rejected'}`,
              `Your Profile Pitch has been ${safetyCheck.safe ? 'Verified' : 'Rejected'}${!safetyCheck.safe ? `: ${safetyCheck.reason}` : ''}.`,
              fcmData,
              notificationData,
              'Profile_Detail',
            );
          }
        }

        console.log(
          `📝 Updated job status user ID: ${pitchDetails[0][0].user_id}`,
        );
      } catch (error) {
        console.error(`⚠️ Error processing job ${job.job_id}:`, error);
      }
    }
  } catch (error) {
    console.error('⚠️ Error fetching pending jobs:', error);
  }
};

// Run the function every 10 seconds
const startJobChecker = () => {
  console.log('🚀 Starting background job checker...');
  setInterval(checkPendingJobs, 10000); // Runs every 10 seconds
};

// Helper function to extract S3 key from the URL
const extractS3Key = (s3Url) => {
  try {
    const urlParts = new URL(s3Url);
    const path = urlParts.pathname;
    console.log(path, 'path');
    return path.startsWith('/') ? path.substring(1) : path;
  } catch (error) {
    throw new Error('Invalid S3 URL format');
  }
};

exports.awsFunctions = { analyzeImage, startJobChecker, startVideoModeration };
