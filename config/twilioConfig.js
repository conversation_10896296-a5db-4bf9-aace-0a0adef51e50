const twilio = require('twilio');
const {
  twilioAccountSid,
  twilioAuthToken,
  twilioServiceSid,
} = require('./index');
const { executeQuery } = require('./dbConfig');

const accountSid = twilioAccountSid;
const authToken = twilioAuthToken;
const client = twilio(accountSid, authToken, {
  lazyLoading: true,
});

const sendOTP = async (userId, phone, country_code) => {
  try {
    // Validate Twilio configuration
    if (!twilioAccountSid || !twilioAuthToken || !twilioServiceSid) {
      throw new Error(
        'Twilio configuration is incomplete. Check environment variables.',
      );
    }

    // Validate phone number format
    if (!phone || phone.length < 10) {
      throw new Error('Invalid phone number format');
    }

    const query = 'CALL SendUserOtp(?, ?, ?);';
    const data = await executeQuery(query, [userId, country_code, phone]);

    // Check if database allows OTP sending
    if (data[0][0].status == 'OTP sent') {
      try {
        const verification = await client.verify.v2
          .services(twilioServiceSid)
          .verifications.create({
            to: phone,
            channel: 'sms',
          });
        console.log('Twilio verification created:', verification.sid);
      } catch (twilioError) {
        console.error('Twilio API Error:', {
          code: twilioError.code,
          message: twilioError.message,
          status: twilioError.status,
          moreInfo: twilioError.moreInfo,
        });

        // Handle specific Twilio errors
        if (twilioError.code === 20003) {
          throw new Error('Authentication failed. Check Twilio credentials.');
        } else if (twilioError.code === 21211) {
          throw new Error('Invalid phone number format for Twilio.');
        } else if (twilioError.code === 21608) {
          throw new Error('Phone number is not verified for trial account.');
        }

        throw twilioError;
      }
    }
    return data[0][0];
  } catch (error) {
    console.error('Error sending OTP:', error.message);
    throw error;
  }
};

// Function to verify OTP
const verifyOTP = async (phone, code) => {
  try {
    const verificationResponse = await client.verify.v2
      .services(twilioServiceSid)
      .verificationChecks.create({
        to: phone,
        code: code,
      });
    console.log(verificationResponse, 'verifyStep');
    return verificationResponse;
  } catch (error) {
    console.error('Error verifying OTP:', error.message);
    throw error;
  }
};

module.exports = { sendOTP, verifyOTP };
