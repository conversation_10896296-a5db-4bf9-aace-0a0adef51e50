require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const helmet = require('helmet');
const swaggerUi = require('swagger-ui-express');
const fs = require('fs');
const path = require('path');
const config = require('./config');
const auth = require('./routes/auth');
const profile = require('./routes/profile');
const common = require('./routes/index.js');
const action = require('./routes/action.js');
const home = require('./routes/home.js');
const admin = require('./routes/AdminRoutes/index.js');
const site = require('./routes/webSite.js');

const notifications = require('./routes/notification.js');
const subscription = require('./routes/subscription.js');
const errorHandler = require('./middlewares/errorHandler');
const headerValidator = require('./validators/headerValidator');
const { awsFunctions } = require('./config/awsConfig.js');
const logUserActivity = require('./middlewares/logHandler.js');
// const { uploadUsersToAlgolia } = require('./utils/algoliaSearch.js');

const app = express();
const port = config.PORT;

// *Middleware setup
app.use(bodyParser.json({ limit: '5mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '5mb' }));
app.use(cors());
app.use(helmet());
// *Header Validator
app.use(headerValidator);
app.use(logUserActivity);
app.get('/', async (req, res) => {
  res.send('Welcome to TechBuddy!!');
});
// *Swagger setup
const swaggerFilePath = path.resolve(__dirname, 'swagger.json');
const swaggerDocument = JSON.parse(fs.readFileSync(swaggerFilePath, 'utf8'));

app.use('/techbuddy', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// *Routes
app.use('/v2', auth);
app.use('/v2/common', common);
app.use('/v2/profile', profile);
app.use('/v2/actions', action);
app.use('/v2/home', home);
app.use('/v2/subscription', subscription);
app.use('/v2/notification', notifications);

//Admin Api

app.use('/v2/adminApi', admin);
app.use('/v2/webSite', site);

// !Error handling
// !app.use(errors()); // Celebrate error Default handler
app.use(errorHandler); // Custom error handler

app.listen(port, () => {
  console.log(`Server running on port ${port}`);
  setTimeout(() => awsFunctions.startJobChecker(), 5000);
  // setTimeout(() => uploadUsersToAlgolia(), 5000);
});
